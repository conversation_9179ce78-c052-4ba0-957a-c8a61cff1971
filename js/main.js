// Main JavaScript functionality for LSTBook

document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    setupMobileMenu();
    setupAuthButtons();
    setupMegaMenu();
    setupAgeFilters();
    setupFavoriteButtons();
    setupGameCards();
    setupVideoEmbeds();
}

// Mobile Menu Toggle
function setupMobileMenu() {
    const mobileMenuToggle = document.getElementById('mobileMenuToggle');
    const navList = document.querySelector('.nav-list');
    
    if (mobileMenuToggle && navList) {
        mobileMenuToggle.addEventListener('click', function() {
            navList.classList.toggle('active');
            const icon = this.querySelector('i');
            if (navList.classList.contains('active')) {
                icon.className = 'fas fa-times';
            } else {
                icon.className = 'fas fa-bars';
            }
        });
    }
}

// Authentication Button Handlers
function setupAuthButtons() {
    const loginBtn = document.getElementById('loginBtn');
    const signupBtn = document.getElementById('signupBtn');
    const logoutBtn = document.getElementById('logoutBtn');
    
    if (loginBtn) {
        loginBtn.addEventListener('click', showLoginModal);
    }
    
    if (signupBtn) {
        signupBtn.addEventListener('click', showSignupModal);
    }
    
    if (logoutBtn) {
        logoutBtn.addEventListener('click', handleLogout);
    }
}

function showLoginModal() {
    const modal = createAuthModal('Login to LSTBook', 'login');
    document.body.appendChild(modal);
}

function showSignupModal() {
    const modal = createAuthModal('Join LSTBook', 'signup');
    document.body.appendChild(modal);
}

function createAuthModal(title, type) {
    const modal = document.createElement('div');
    modal.className = 'auth-modal';
    modal.innerHTML = `
        <div class="auth-modal-content">
            <div class="auth-modal-header">
                <h3><i class="fas fa-user-circle"></i> ${title}</h3>
                <button class="close-modal"><i class="fas fa-times"></i></button>
            </div>
            <div class="auth-modal-body">
                <form class="auth-form" id="authForm">
                    <div class="form-group">
                        <label for="email"><i class="fas fa-envelope"></i> Email</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                    <div class="form-group">
                        <label for="password"><i class="fas fa-lock"></i> Password</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    <button type="submit" class="btn btn-primary auth-submit">
                        <i class="fas fa-sign-in-alt"></i> ${type === 'login' ? 'Login' : 'Sign Up'}
                    </button>
                </form>
                <div class="auth-divider">
                    <span>or</span>
                </div>
                <button class="btn btn-secondary anonymous-login">
                    <i class="fas fa-user-secret"></i> Continue as Guest
                </button>
                <div class="auth-error" id="authError" style="display: none;"></div>
            </div>
        </div>
    `;
    
    // Add modal styles
    const style = document.createElement('style');
    style.textContent = `
        .auth-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        }
        .auth-modal-content {
            background: white;
            border-radius: 20px;
            width: 90%;
            max-width: 400px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        .auth-modal-header {
            background: linear-gradient(135deg, #FFB703, #FB8500);
            color: white;
            padding: 20px;
            border-radius: 20px 20px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .auth-modal-header h3 {
            margin: 0;
            font-size: 1.3rem;
        }
        .close-modal {
            background: none;
            border: none;
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: background 0.3s ease;
        }
        .close-modal:hover {
            background: rgba(255,255,255,0.2);
        }
        .auth-modal-body {
            padding: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #023047;
            font-weight: 600;
        }
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #8ECAE6;
            border-radius: 10px;
            font-size: 1rem;
            font-family: inherit;
            transition: border-color 0.3s ease;
        }
        .form-group input:focus {
            outline: none;
            border-color: #219EBC;
        }
        .auth-submit {
            width: 100%;
            margin-bottom: 20px;
        }
        .auth-divider {
            text-align: center;
            margin: 20px 0;
            position: relative;
            color: #666;
        }
        .auth-divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #ddd;
        }
        .auth-divider span {
            background: white;
            padding: 0 15px;
            position: relative;
        }
        .anonymous-login {
            width: 100%;
        }
        .auth-error {
            background: #ffebee;
            color: #c62828;
            padding: 10px;
            border-radius: 8px;
            margin-top: 15px;
            text-align: center;
        }
    `;
    document.head.appendChild(style);
    
    // Event listeners
    modal.querySelector('.close-modal').addEventListener('click', () => {
        document.body.removeChild(modal);
    });
    
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            document.body.removeChild(modal);
        }
    });
    
    modal.querySelector('#authForm').addEventListener('submit', (e) => {
        e.preventDefault();
        handleAuthSubmit(type, modal);
    });
    
    modal.querySelector('.anonymous-login').addEventListener('click', () => {
        handleAnonymousLogin(modal);
    });
    
    return modal;
}

function handleAuthSubmit(type, modal) {
    const email = modal.querySelector('#email').value;
    const password = modal.querySelector('#password').value;
    const errorDiv = modal.querySelector('#authError');
    
    const authFunction = type === 'login' ? 
        window.firebaseAuth.signInWithEmail : 
        window.firebaseAuth.signUpWithEmail;
    
    authFunction(email, password)
        .then(() => {
            document.body.removeChild(modal);
        })
        .catch((error) => {
            errorDiv.textContent = error.message;
            errorDiv.style.display = 'block';
        });
}

function handleAnonymousLogin(modal) {
    window.firebaseAuth.signInAnonymously()
        .then(() => {
            document.body.removeChild(modal);
        })
        .catch((error) => {
            const errorDiv = modal.querySelector('#authError');
            errorDiv.textContent = error.message;
            errorDiv.style.display = 'block';
        });
}

function handleLogout() {
    window.firebaseAuth.signOut()
        .then(() => {
            // Redirect to home if on favorites page
            if (window.location.pathname.includes('favorites.html')) {
                window.location.href = 'index.html';
            }
        })
        .catch((error) => {
            console.error('Logout error:', error);
        });
}

// Mega Menu for Mobile
function setupMegaMenu() {
    const megaMenuTrigger = document.querySelector('.has-mega-menu > .nav-link');
    const megaMenu = document.querySelector('.mega-menu');
    
    if (megaMenuTrigger && megaMenu && window.innerWidth <= 768) {
        megaMenuTrigger.addEventListener('click', (e) => {
            e.preventDefault();
            megaMenu.style.position = 'static';
            megaMenu.style.opacity = megaMenu.style.opacity === '1' ? '0' : '1';
            megaMenu.style.visibility = megaMenu.style.visibility === 'visible' ? 'hidden' : 'visible';
        });
    }
}

// Age Filter Functionality
function setupAgeFilters() {
    const ageFilter = document.getElementById('ageFilter');
    const categoryFilter = document.getElementById('categoryFilter');

    if (ageFilter) {
        ageFilter.addEventListener('change', filterGames);
    }

    if (categoryFilter) {
        categoryFilter.addEventListener('change', filterGames);
    }
}

function filterGamesByAge() {
    filterGames();
}

function filterGames() {
    const selectedAge = document.getElementById('ageFilter')?.value || 'all';
    const selectedCategory = document.getElementById('categoryFilter')?.value || 'all';
    const gameCards = document.querySelectorAll('.game-card');

    gameCards.forEach(card => {
        const ageRange = card.dataset.ageRange;
        const category = card.dataset.category;

        const ageMatch = selectedAge === 'all' || ageRange === selectedAge;
        const categoryMatch = selectedCategory === 'all' || category === selectedCategory;

        if (ageMatch && categoryMatch) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

// Favorite Button Functionality
function setupFavoriteButtons() {
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('favorite-btn') || e.target.parentElement.classList.contains('favorite-btn')) {
            const btn = e.target.classList.contains('favorite-btn') ? e.target : e.target.parentElement;
            handleFavoriteToggle(btn);
        }
    });
}

function handleFavoriteToggle(btn) {
    const gameCard = btn.closest('.game-card');
    const gameId = gameCard.dataset.gameId;
    const gameData = {
        title: gameCard.querySelector('.game-title').textContent,
        description: gameCard.querySelector('.game-description').textContent,
        ageRange: gameCard.dataset.ageRange,
        category: gameCard.dataset.category,
        thumbnail: gameCard.querySelector('.game-thumbnail').src
    };
    
    if (btn.classList.contains('favorited')) {
        // Remove from favorites
        window.firebaseAuth.removeFromFavorites(gameId)
            .then(() => {
                btn.classList.remove('favorited');
                btn.innerHTML = '<i class="far fa-heart"></i>';
                btn.title = 'Add to Favorites';
            })
            .catch((error) => {
                if (error === 'User not logged in') {
                    showLoginModal();
                } else {
                    console.error('Error removing favorite:', error);
                }
            });
    } else {
        // Add to favorites
        window.firebaseAuth.addToFavorites(gameId, gameData)
            .then(() => {
                btn.classList.add('favorited');
                btn.innerHTML = '<i class="fas fa-heart"></i>';
                btn.title = 'Remove from Favorites';
            })
            .catch((error) => {
                if (error === 'User not logged in') {
                    showLoginModal();
                } else {
                    console.error('Error adding favorite:', error);
                }
            });
    }
}

// Game Card Click Tracking
function setupGameCards() {
    document.addEventListener('click', function(e) {
        const gameCard = e.target.closest('.game-card');
        if (gameCard && !e.target.closest('.favorite-btn')) {
            const gameId = gameCard.dataset.gameId;
            const gameTitle = gameCard.querySelector('.game-title').textContent;
            const category = gameCard.dataset.category;
            
            // Track game play
            window.firebaseAuth.trackGamePlay(gameId, gameTitle, category);
        }
    });
}

// Video Embed Setup
function setupVideoEmbeds() {
    const videoCards = document.querySelectorAll('.video-card');
    videoCards.forEach(card => {
        card.addEventListener('click', function() {
            const videoId = this.dataset.videoId;
            const videoTitle = this.querySelector('.video-title').textContent;
            
            // Track video watch
            window.firebaseAuth.trackVideoWatch(videoId, videoTitle);
        });
    });
}

// Load user favorites (for favorites page)
function loadUserFavorites() {
    const favoritesContainer = document.getElementById('favoritesContainer');
    if (!favoritesContainer) return;
    
    window.firebaseAuth.getUserFavorites()
        .then((favorites) => {
            if (favorites.length === 0) {
                favoritesContainer.innerHTML = `
                    <div class="no-favorites">
                        <i class="fas fa-heart-broken"></i>
                        <h3>No favorites yet!</h3>
                        <p>Start exploring games and add your favorites here.</p>
                        <a href="games.html" class="btn btn-primary">
                            <i class="fas fa-gamepad"></i> Browse Games
                        </a>
                    </div>
                `;
            } else {
                favoritesContainer.innerHTML = favorites.map(game => `
                    <div class="game-card" data-game-id="${game.id}" data-age-range="${game.ageRange}" data-category="${game.category}">
                        <div class="game-thumbnail">
                            <img src="${game.thumbnail}" alt="${game.title}">
                            <div class="game-overlay">
                                <button class="btn btn-play">
                                    <i class="fas fa-play"></i> Play Now
                                </button>
                            </div>
                        </div>
                        <div class="game-info">
                            <div class="game-header">
                                <h3 class="game-title">${game.title}</h3>
                                <button class="favorite-btn favorited" title="Remove from Favorites">
                                    <i class="fas fa-heart"></i>
                                </button>
                            </div>
                            <p class="game-description">${game.description}</p>
                            <div class="game-meta">
                                <span class="age-badge">Ages ${game.ageRange}</span>
                                <span class="category-badge">${game.category}</span>
                            </div>
                        </div>
                    </div>
                `).join('');
            }
        })
        .catch((error) => {
            console.error('Error loading favorites:', error);
            favoritesContainer.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h3>Oops! Something went wrong</h3>
                    <p>We couldn't load your favorites. Please try again later.</p>
                </div>
            `;
        });
}

// Utility function to get URL parameters
function getUrlParameter(name) {
    name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
    const regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
    const results = regex.exec(location.search);
    return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
}

// Filter games by category (for games page)
function filterByCategory() {
    const category = getUrlParameter('category');
    if (category) {
        const gameCards = document.querySelectorAll('.game-card');
        gameCards.forEach(card => {
            if (card.dataset.category === category) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
        
        // Update page title
        const pageTitle = document.querySelector('.page-title');
        if (pageTitle) {
            const categoryNames = {
                'animals': 'Animals',
                'math': 'Numbers & Math',
                'letters': 'Letters & Words',
                'shapes': 'Shapes & Colors',
                'logic': 'Logic & Puzzles',
                'memory': 'Memory Games',
                'music': 'Music & Sounds',
                'stories': 'Interactive Stories',
                'coding': 'Learn to Code',
                'art': 'Creativity & Art'
            };
            pageTitle.textContent = categoryNames[category] || 'Games';
        }
    }
}

// Initialize category filtering on games page
if (window.location.pathname.includes('games.html')) {
    document.addEventListener('DOMContentLoaded', filterByCategory);
}

// Check favorite status for game cards
function updateFavoriteButtons() {
    const gameCards = document.querySelectorAll('.game-card');
    gameCards.forEach(card => {
        const gameId = card.dataset.gameId;
        const favoriteBtn = card.querySelector('.favorite-btn');

        if (favoriteBtn) {
            window.firebaseAuth.checkIfFavorite(gameId)
                .then((isFavorite) => {
                    if (isFavorite) {
                        favoriteBtn.classList.add('favorited');
                        favoriteBtn.innerHTML = '<i class="fas fa-heart"></i>';
                        favoriteBtn.title = 'Remove from Favorites';
                    } else {
                        favoriteBtn.classList.remove('favorited');
                        favoriteBtn.innerHTML = '<i class="far fa-heart"></i>';
                        favoriteBtn.title = 'Add to Favorites';
                    }
                });
        }
    });
}

// Update favorite buttons when auth state changes
if (typeof firebase !== 'undefined') {
    firebase.auth().onAuthStateChanged(() => {
        setTimeout(updateFavoriteButtons, 500);
    });
}
