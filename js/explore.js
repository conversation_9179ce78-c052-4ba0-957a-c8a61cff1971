// Explore Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initializeExplorePage();
});

function initializeExplorePage() {
    setupQuizFunctionality();
    setupActivityButtons();
    animateElements();
}

// Quiz Data
const quizQuestions = [
    {
        question: "What color do you get when you mix red and yellow?",
        options: ["Purple", "Orange", "Green", "Blue"],
        correct: 1,
        explanation: "Great job! Orange is correct!"
    },
    {
        question: "How many legs does a spider have?",
        options: ["6", "8", "10", "4"],
        correct: 1,
        explanation: "Excellent! Spiders have 8 legs!"
    },
    {
        question: "What shape has 3 sides?",
        options: ["Circle", "Square", "Triangle", "Rectangle"],
        correct: 2,
        explanation: "Perfect! A triangle has 3 sides!"
    },
    {
        question: "Which planet is closest to the Sun?",
        options: ["Earth", "Mars", "Mercury", "Venus"],
        correct: 2,
        explanation: "Amazing! Mercury is the closest planet to the Sun!"
    },
    {
        question: "What do bees make?",
        options: ["Milk", "Honey", "Butter", "Cheese"],
        correct: 1,
        explanation: "Sweet! Bees make honey!"
    }
];

let currentQuizIndex = 0;

function setupQuizFunctionality() {
    const quizOptions = document.querySelectorAll('.quiz-option');
    
    quizOptions.forEach(option => {
        option.addEventListener('click', function() {
            handleQuizAnswer(this);
        });
    });
    
    // Load first question
    loadQuizQuestion(currentQuizIndex);
}

function loadQuizQuestion(index) {
    const question = quizQuestions[index];
    const quizCard = document.getElementById('quizCard');
    
    if (!quizCard || !question) return;
    
    // Update question
    const questionElement = quizCard.querySelector('.quiz-question h3');
    if (questionElement) {
        questionElement.textContent = question.question;
    }
    
    // Update options
    const optionElements = quizCard.querySelectorAll('.quiz-option');
    optionElements.forEach((option, i) => {
        option.textContent = question.options[i];
        option.dataset.answer = i === question.correct ? 'correct' : 'wrong';
        option.classList.remove('selected', 'correct', 'wrong');
        option.disabled = false;
    });
    
    // Hide result
    const quizResult = document.getElementById('quizResult');
    if (quizResult) {
        quizResult.style.display = 'none';
    }
}

function handleQuizAnswer(selectedOption) {
    const allOptions = document.querySelectorAll('.quiz-option');
    const quizResult = document.getElementById('quizResult');
    const isCorrect = selectedOption.dataset.answer === 'correct';
    
    // Disable all options
    allOptions.forEach(option => {
        option.disabled = true;
        if (option.dataset.answer === 'correct') {
            option.classList.add('correct');
        } else if (option === selectedOption && !isCorrect) {
            option.classList.add('wrong');
        }
    });
    
    // Show result
    if (quizResult) {
        const resultIcon = quizResult.querySelector('.result-icon i');
        const resultText = quizResult.querySelector('.result-text');
        const currentQuestion = quizQuestions[currentQuizIndex];
        
        if (isCorrect) {
            resultIcon.className = 'fas fa-check-circle';
            resultIcon.style.color = '#28a745';
            resultText.textContent = currentQuestion.explanation;
        } else {
            resultIcon.className = 'fas fa-times-circle';
            resultIcon.style.color = '#dc3545';
            resultText.textContent = `Oops! The correct answer is: ${currentQuestion.options[currentQuestion.correct]}`;
        }
        
        quizResult.style.display = 'block';
        
        // Animate result appearance
        quizResult.style.opacity = '0';
        quizResult.style.transform = 'translateY(20px)';
        setTimeout(() => {
            quizResult.style.transition = 'all 0.5s ease';
            quizResult.style.opacity = '1';
            quizResult.style.transform = 'translateY(0)';
        }, 100);
    }
    
    // Track quiz interaction
    trackQuizAnswer(currentQuizIndex, isCorrect);
}

function nextQuiz() {
    currentQuizIndex = (currentQuizIndex + 1) % quizQuestions.length;
    loadQuizQuestion(currentQuizIndex);
}

function trackQuizAnswer(questionIndex, isCorrect) {
    // Track quiz performance
    console.log('Quiz answered:', {
        questionIndex: questionIndex,
        correct: isCorrect,
        timestamp: new Date().toISOString()
    });
    
    // You could save this to Firebase for progress tracking
    if (window.firebaseAuth && window.firebaseAuth.saveGameProgress) {
        window.firebaseAuth.saveGameProgress('explore_quiz', {
            questionIndex: questionIndex,
            correct: isCorrect,
            totalQuestions: quizQuestions.length
        });
    }
}

// Activity Button Functionality
function setupActivityButtons() {
    const activityButtons = document.querySelectorAll('.activity-card .btn');
    
    activityButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const activityCard = this.closest('.activity-card');
            const activityTitle = activityCard.querySelector('h3').textContent;
            
            handleActivityClick(activityTitle, this);
        });
    });
}

function handleActivityClick(activityTitle, button) {
    // Show activity modal or redirect
    showActivityModal(activityTitle);
    
    // Track activity interaction
    console.log('Activity clicked:', activityTitle);
    
    // Add visual feedback
    button.style.transform = 'scale(0.95)';
    setTimeout(() => {
        button.style.transform = 'scale(1)';
    }, 150);
}

function showActivityModal(activityTitle) {
    // Create a simple modal for activity instructions
    const modal = document.createElement('div');
    modal.className = 'activity-modal';
    modal.innerHTML = `
        <div class="activity-modal-content">
            <div class="activity-modal-header">
                <h3><i class="fas fa-star"></i> ${activityTitle}</h3>
                <button class="close-activity-modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="activity-modal-body">
                ${getActivityInstructions(activityTitle)}
                <div class="activity-modal-actions">
                    <button class="btn btn-primary" onclick="startActivity('${activityTitle}')">
                        <i class="fas fa-play"></i> Let's Do It!
                    </button>
                    <button class="btn btn-secondary close-activity-modal">
                        <i class="fas fa-times"></i> Maybe Later
                    </button>
                </div>
            </div>
        </div>
    `;
    
    // Add modal styles
    const style = document.createElement('style');
    style.textContent = `
        .activity-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            animation: fadeIn 0.3s ease;
        }
        .activity-modal-content {
            background: white;
            border-radius: 20px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            animation: slideUp 0.3s ease;
        }
        .activity-modal-header {
            background: linear-gradient(135deg, #FFB703, #FB8500);
            color: white;
            padding: 20px;
            border-radius: 20px 20px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .activity-modal-header h3 {
            margin: 0;
            font-size: 1.3rem;
        }
        .close-activity-modal {
            background: none;
            border: none;
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: background 0.3s ease;
        }
        .close-activity-modal:hover {
            background: rgba(255,255,255,0.2);
        }
        .activity-modal-body {
            padding: 30px;
        }
        .activity-modal-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 25px;
            flex-wrap: wrap;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        @keyframes slideUp {
            from { transform: translateY(50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
    `;
    document.head.appendChild(style);
    
    // Event listeners
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            document.body.removeChild(modal);
        }
    });
    
    modal.querySelectorAll('.close-activity-modal').forEach(btn => {
        btn.addEventListener('click', () => {
            document.body.removeChild(modal);
        });
    });
    
    document.body.appendChild(modal);
}

function getActivityInstructions(activityTitle) {
    const instructions = {
        'Drawing Challenge': `
            <p><strong>Let's get creative!</strong></p>
            <p>🎨 Get some paper and crayons or colored pencils</p>
            <p>🐘 Draw your favorite animal</p>
            <p>✨ Give it a superpower (like flying, invisibility, or super strength!)</p>
            <p>🌈 Use lots of colors to make it special</p>
            <p>📸 Ask a grown-up to take a picture when you're done!</p>
        `,
        'Make Up a Song': `
            <p><strong>Time to be a musician!</strong></p>
            <p>🎵 Think about what you did today</p>
            <p>🎶 Make up words that rhyme (like "play" and "day")</p>
            <p>🎤 Sing it with a fun melody</p>
            <p>👏 Clap your hands to keep the beat</p>
            <p>🎭 Perform it for your family!</p>
        `,
        'Story Time': `
            <p><strong>Become a storyteller!</strong></p>
            <p>📖 Think of a magical place you'd like to visit</p>
            <p>🧙‍♀️ Who would you meet there?</p>
            <p>✨ What magical things would happen?</p>
            <p>📝 Write or tell your story to someone</p>
            <p>🎨 Draw pictures to go with your story!</p>
        `,
        'Nature Hunt': `
            <p><strong>Let's explore outside!</strong></p>
            <p>🔍 Go to your backyard, park, or anywhere with nature</p>
            <p>🔺 Look for triangle shapes (like pine tree tops)</p>
            <p>⭕ Find circle shapes (like flowers or tree rings)</p>
            <p>⬜ Spot square shapes (like fence posts)</p>
            <p>📋 Make a list of all the shapes you find!</p>
        `
    };
    
    return instructions[activityTitle] || '<p>Let\'s have fun with this activity!</p>';
}

function startActivity(activityTitle) {
    // Close modal
    const modal = document.querySelector('.activity-modal');
    if (modal) {
        document.body.removeChild(modal);
    }
    
    // Show encouragement message
    showEncouragementMessage(activityTitle);
    
    // Track activity start
    console.log('Activity started:', activityTitle);
}

function showEncouragementMessage(activityTitle) {
    const message = document.createElement('div');
    message.className = 'encouragement-message';
    message.innerHTML = `
        <div class="encouragement-content">
            <i class="fas fa-star"></i>
            <h3>Have Fun with ${activityTitle}!</h3>
            <p>Remember, there's no wrong way to be creative. Enjoy yourself!</p>
        </div>
    `;
    
    const style = document.createElement('style');
    style.textContent = `
        .encouragement-message {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.2);
            z-index: 10000;
            animation: slideInRight 0.5s ease, fadeOut 0.5s ease 3s forwards;
            max-width: 300px;
        }
        .encouragement-content {
            text-align: center;
        }
        .encouragement-content i {
            font-size: 2rem;
            margin-bottom: 10px;
            display: block;
        }
        .encouragement-content h3 {
            margin: 0 0 10px 0;
            font-size: 1.1rem;
        }
        .encouragement-content p {
            margin: 0;
            font-size: 0.9rem;
            opacity: 0.9;
        }
        @keyframes slideInRight {
            from { transform: translateX(100%); }
            to { transform: translateX(0); }
        }
        @keyframes fadeOut {
            to { opacity: 0; transform: translateX(100%); }
        }
    `;
    document.head.appendChild(style);
    
    document.body.appendChild(message);
    
    // Remove message after animation
    setTimeout(() => {
        if (document.body.contains(message)) {
            document.body.removeChild(message);
        }
    }, 4000);
}

// Animate elements on scroll
function animateElements() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animation = 'fadeInUp 0.6s ease forwards';
            }
        });
    }, observerOptions);
    
    // Observe all cards
    const cards = document.querySelectorAll('.fact-card, .activity-card, .tip-card');
    cards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        observer.observe(card);
    });
}

// Export functions for global use
window.explorePage = {
    nextQuiz,
    startActivity,
    handleActivityClick
};
