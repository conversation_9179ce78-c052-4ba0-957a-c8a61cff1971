// Contact Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initializeContactPage();
});

function initializeContactPage() {
    setupContactForm();
    setupFormValidation();
    animateElements();
}

function setupContactForm() {
    const contactForm = document.getElementById('contactForm');
    
    if (contactForm) {
        contactForm.addEventListener('submit', handleFormSubmission);
    }
}

function handleFormSubmission(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const contactData = {
        firstName: formData.get('firstName'),
        lastName: formData.get('lastName'),
        email: formData.get('email'),
        subject: formData.get('subject'),
        message: formData.get('message'),
        timestamp: new Date().toISOString()
    };
    
    // Validate form data
    if (!validateContactForm(contactData)) {
        return;
    }
    
    // Show loading state
    showFormLoading();
    
    // Submit to Firebase (or your backend)
    submitContactForm(contactData)
        .then(() => {
            showFormSuccess();
            e.target.reset();
        })
        .catch((error) => {
            console.error('Error submitting form:', error);
            showFormError();
        });
}

function validateContactForm(data) {
    const errors = [];
    
    // Validate required fields
    if (!data.firstName.trim()) {
        errors.push('First name is required');
    }
    
    if (!data.lastName.trim()) {
        errors.push('Last name is required');
    }
    
    if (!data.email.trim()) {
        errors.push('Email is required');
    } else if (!isValidEmail(data.email)) {
        errors.push('Please enter a valid email address');
    }
    
    if (!data.subject) {
        errors.push('Please select a subject');
    }
    
    if (!data.message.trim()) {
        errors.push('Message is required');
    } else if (data.message.trim().length < 10) {
        errors.push('Message must be at least 10 characters long');
    }
    
    if (errors.length > 0) {
        showFormErrors(errors);
        return false;
    }
    
    return true;
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function showFormErrors(errors) {
    // Remove existing error messages
    const existingErrors = document.querySelectorAll('.form-error');
    existingErrors.forEach(error => error.remove());
    
    // Create error container
    const errorContainer = document.createElement('div');
    errorContainer.className = 'form-error';
    errorContainer.innerHTML = `
        <div class="error-content">
            <i class="fas fa-exclamation-triangle"></i>
            <h4>Please fix the following errors:</h4>
            <ul>
                ${errors.map(error => `<li>${error}</li>`).join('')}
            </ul>
        </div>
    `;
    
    // Add error styles
    const style = document.createElement('style');
    style.textContent = `
        .form-error {
            background: #ffebee;
            border: 1px solid #f44336;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            animation: shake 0.5s ease;
        }
        .error-content {
            display: flex;
            align-items: flex-start;
            gap: 15px;
        }
        .error-content i {
            color: #f44336;
            font-size: 1.5rem;
            margin-top: 2px;
        }
        .error-content h4 {
            color: #d32f2f;
            margin: 0 0 10px 0;
            font-size: 1.1rem;
        }
        .error-content ul {
            margin: 0;
            padding-left: 20px;
            color: #d32f2f;
        }
        .error-content li {
            margin-bottom: 5px;
        }
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
    `;
    document.head.appendChild(style);
    
    // Insert error before form
    const contactForm = document.getElementById('contactForm');
    contactForm.parentNode.insertBefore(errorContainer, contactForm);
    
    // Scroll to error
    errorContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
}

function showFormLoading() {
    const submitBtn = document.querySelector('.submit-btn');
    if (submitBtn) {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending Message...';
    }
}

function showFormSuccess() {
    // Remove any existing messages
    const existingMessages = document.querySelectorAll('.form-message');
    existingMessages.forEach(msg => msg.remove());
    
    const successMessage = document.createElement('div');
    successMessage.className = 'form-message form-success';
    successMessage.innerHTML = `
        <div class="success-content">
            <i class="fas fa-check-circle"></i>
            <div>
                <h4>Message Sent Successfully!</h4>
                <p>Thank you for contacting us! We'll get back to you within 24 hours.</p>
            </div>
        </div>
    `;
    
    // Add success styles
    const style = document.createElement('style');
    style.textContent = `
        .form-success {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            animation: slideDown 0.5s ease;
        }
        .success-content {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .success-content i {
            color: #4caf50;
            font-size: 2rem;
        }
        .success-content h4 {
            color: #2e7d32;
            margin: 0 0 5px 0;
            font-size: 1.2rem;
        }
        .success-content p {
            color: #388e3c;
            margin: 0;
        }
        @keyframes slideDown {
            from { transform: translateY(-20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
    `;
    document.head.appendChild(style);
    
    // Insert success message
    const contactForm = document.getElementById('contactForm');
    contactForm.parentNode.insertBefore(successMessage, contactForm);
    
    // Reset submit button
    const submitBtn = document.querySelector('.submit-btn');
    if (submitBtn) {
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-paper-plane"></i> Send Message';
    }
    
    // Scroll to success message
    successMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
    
    // Remove success message after 5 seconds
    setTimeout(() => {
        if (document.body.contains(successMessage)) {
            successMessage.style.animation = 'fadeOut 0.5s ease forwards';
            setTimeout(() => {
                if (document.body.contains(successMessage)) {
                    successMessage.remove();
                }
            }, 500);
        }
    }, 5000);
}

function showFormError() {
    // Remove any existing messages
    const existingMessages = document.querySelectorAll('.form-message');
    existingMessages.forEach(msg => msg.remove());
    
    const errorMessage = document.createElement('div');
    errorMessage.className = 'form-message form-error-message';
    errorMessage.innerHTML = `
        <div class="error-message-content">
            <i class="fas fa-exclamation-circle"></i>
            <div>
                <h4>Oops! Something went wrong</h4>
                <p>We couldn't send your message right now. Please try again later or email us <NAME_EMAIL></p>
            </div>
        </div>
    `;
    
    // Add error styles
    const style = document.createElement('style');
    style.textContent = `
        .form-error-message {
            background: #ffebee;
            border: 1px solid #f44336;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            animation: slideDown 0.5s ease;
        }
        .error-message-content {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .error-message-content i {
            color: #f44336;
            font-size: 2rem;
        }
        .error-message-content h4 {
            color: #d32f2f;
            margin: 0 0 5px 0;
            font-size: 1.2rem;
        }
        .error-message-content p {
            color: #d32f2f;
            margin: 0;
        }
        @keyframes fadeOut {
            to { opacity: 0; transform: translateY(-20px); }
        }
    `;
    document.head.appendChild(style);
    
    // Insert error message
    const contactForm = document.getElementById('contactForm');
    contactForm.parentNode.insertBefore(errorMessage, contactForm);
    
    // Reset submit button
    const submitBtn = document.querySelector('.submit-btn');
    if (submitBtn) {
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-paper-plane"></i> Send Message';
    }
    
    // Scroll to error message
    errorMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
}

function submitContactForm(contactData) {
    // Submit to Firebase Firestore
    if (typeof firebase !== 'undefined' && firebase.firestore) {
        return firebase.firestore().collection('contact_messages').add(contactData);
    } else {
        // Fallback: simulate API call
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                // Simulate success (90% of the time)
                if (Math.random() > 0.1) {
                    console.log('Contact form submitted:', contactData);
                    resolve();
                } else {
                    reject(new Error('Simulated network error'));
                }
            }, 2000);
        });
    }
}

function setupFormValidation() {
    // Real-time validation
    const formInputs = document.querySelectorAll('#contactForm input, #contactForm select, #contactForm textarea');
    
    formInputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });
        
        input.addEventListener('input', function() {
            // Remove error styling when user starts typing
            this.classList.remove('error');
            const errorMsg = this.parentNode.querySelector('.field-error');
            if (errorMsg) {
                errorMsg.remove();
            }
        });
    });
}

function validateField(field) {
    const value = field.value.trim();
    let isValid = true;
    let errorMessage = '';
    
    // Remove existing error
    field.classList.remove('error');
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
    
    // Validate based on field type
    switch (field.name) {
        case 'firstName':
        case 'lastName':
            if (!value) {
                isValid = false;
                errorMessage = 'This field is required';
            }
            break;
        case 'email':
            if (!value) {
                isValid = false;
                errorMessage = 'Email is required';
            } else if (!isValidEmail(value)) {
                isValid = false;
                errorMessage = 'Please enter a valid email address';
            }
            break;
        case 'subject':
            if (!value) {
                isValid = false;
                errorMessage = 'Please select a subject';
            }
            break;
        case 'message':
            if (!value) {
                isValid = false;
                errorMessage = 'Message is required';
            } else if (value.length < 10) {
                isValid = false;
                errorMessage = 'Message must be at least 10 characters long';
            }
            break;
    }
    
    if (!isValid) {
        field.classList.add('error');
        const errorElement = document.createElement('div');
        errorElement.className = 'field-error';
        errorElement.textContent = errorMessage;
        field.parentNode.appendChild(errorElement);
        
        // Add error styles if not already added
        if (!document.querySelector('#field-error-styles')) {
            const style = document.createElement('style');
            style.id = 'field-error-styles';
            style.textContent = `
                .form-group input.error,
                .form-group select.error,
                .form-group textarea.error {
                    border-color: #f44336;
                    box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.2);
                }
                .field-error {
                    color: #f44336;
                    font-size: 0.85rem;
                    margin-top: 5px;
                    display: flex;
                    align-items: center;
                    gap: 5px;
                }
                .field-error::before {
                    content: '⚠';
                    font-size: 0.9rem;
                }
            `;
            document.head.appendChild(style);
        }
    }
    
    return isValid;
}

// Animate elements on scroll
function animateElements() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animation = 'fadeInUp 0.6s ease forwards';
            }
        });
    }, observerOptions);
    
    // Observe contact items and FAQ items
    const items = document.querySelectorAll('.contact-item, .faq-item');
    items.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(30px)';
        item.style.animationDelay = `${index * 0.1}s`;
        observer.observe(item);
    });
}

// Export functions for global use
window.contactPage = {
    submitContactForm,
    validateContactForm
};
