// Firebase Configuration
// TODO: Replace with your actual Firebase config from Firebase Console
const firebaseConfig = {
    apiKey: "YOUR_API_KEY_HERE",
    authDomain: "YOUR_PROJECT_ID.firebaseapp.com",
    projectId: "YOUR_PROJECT_ID",
    storageBucket: "YOUR_PROJECT_ID.appspot.com",
    messagingSenderId: "YOUR_MESSAGING_SENDER_ID",
    appId: "YOUR_APP_ID"
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);

// Initialize Firebase services
const auth = firebase.auth();
const db = firebase.firestore();

// Auth state observer
auth.onAuthStateChanged((user) => {
    const loginBtn = document.getElementById('loginBtn');
    const signupBtn = document.getElementById('signupBtn');
    const userMenu = document.getElementById('userMenu');
    const userName = document.getElementById('userName');
    
    if (user) {
        // User is signed in
        console.log('User signed in:', user.email || 'Anonymous');
        
        if (loginBtn) loginBtn.style.display = 'none';
        if (signupBtn) signupBtn.style.display = 'none';
        if (userMenu) userMenu.style.display = 'flex';
        if (userName) userName.textContent = user.email ? `Welcome, ${user.email.split('@')[0]}!` : 'Welcome!';
        
        // Update favorites page if we're on it
        if (window.location.pathname.includes('favorites.html')) {
            loadUserFavorites();
        }
    } else {
        // User is signed out
        console.log('User signed out');
        
        if (loginBtn) loginBtn.style.display = 'inline-block';
        if (signupBtn) signupBtn.style.display = 'inline-block';
        if (userMenu) userMenu.style.display = 'none';
        
        // Redirect from favorites page if not logged in
        if (window.location.pathname.includes('favorites.html')) {
            window.location.href = 'index.html';
        }
    }
});

// Authentication functions
function signInWithEmail(email, password) {
    return auth.signInWithEmailAndPassword(email, password)
        .then((userCredential) => {
            console.log('Sign in successful');
            return userCredential.user;
        })
        .catch((error) => {
            console.error('Sign in error:', error);
            throw error;
        });
}

function signUpWithEmail(email, password) {
    return auth.createUserWithEmailAndPassword(email, password)
        .then((userCredential) => {
            console.log('Sign up successful');
            return userCredential.user;
        })
        .catch((error) => {
            console.error('Sign up error:', error);
            throw error;
        });
}

function signInAnonymously() {
    return auth.signInAnonymously()
        .then((userCredential) => {
            console.log('Anonymous sign in successful');
            return userCredential.user;
        })
        .catch((error) => {
            console.error('Anonymous sign in error:', error);
            throw error;
        });
}

function signOut() {
    return auth.signOut()
        .then(() => {
            console.log('Sign out successful');
        })
        .catch((error) => {
            console.error('Sign out error:', error);
            throw error;
        });
}

// Firestore functions for favorites
function addToFavorites(gameId, gameData) {
    const user = auth.currentUser;
    if (!user) {
        console.error('User must be logged in to add favorites');
        return Promise.reject('User not logged in');
    }
    
    return db.collection('users').doc(user.uid).collection('favorites').doc(gameId).set({
        ...gameData,
        addedAt: firebase.firestore.FieldValue.serverTimestamp()
    })
    .then(() => {
        console.log('Game added to favorites');
    })
    .catch((error) => {
        console.error('Error adding to favorites:', error);
        throw error;
    });
}

function removeFromFavorites(gameId) {
    const user = auth.currentUser;
    if (!user) {
        console.error('User must be logged in to remove favorites');
        return Promise.reject('User not logged in');
    }
    
    return db.collection('users').doc(user.uid).collection('favorites').doc(gameId).delete()
    .then(() => {
        console.log('Game removed from favorites');
    })
    .catch((error) => {
        console.error('Error removing from favorites:', error);
        throw error;
    });
}

function getUserFavorites() {
    const user = auth.currentUser;
    if (!user) {
        console.error('User must be logged in to get favorites');
        return Promise.reject('User not logged in');
    }
    
    return db.collection('users').doc(user.uid).collection('favorites')
        .orderBy('addedAt', 'desc')
        .get()
        .then((querySnapshot) => {
            const favorites = [];
            querySnapshot.forEach((doc) => {
                favorites.push({
                    id: doc.id,
                    ...doc.data()
                });
            });
            return favorites;
        })
        .catch((error) => {
            console.error('Error getting favorites:', error);
            throw error;
        });
}

function checkIfFavorite(gameId) {
    const user = auth.currentUser;
    if (!user) {
        return Promise.resolve(false);
    }
    
    return db.collection('users').doc(user.uid).collection('favorites').doc(gameId).get()
        .then((doc) => {
            return doc.exists;
        })
        .catch((error) => {
            console.error('Error checking favorite status:', error);
            return false;
        });
}

// Progress tracking functions (placeholder for future implementation)
function saveGameProgress(gameId, progressData) {
    const user = auth.currentUser;
    if (!user) {
        console.log('User not logged in - progress not saved');
        return Promise.resolve();
    }
    
    return db.collection('users').doc(user.uid).collection('progress').doc(gameId).set({
        ...progressData,
        lastPlayed: firebase.firestore.FieldValue.serverTimestamp()
    }, { merge: true })
    .then(() => {
        console.log('Game progress saved');
    })
    .catch((error) => {
        console.error('Error saving progress:', error);
    });
}

function getGameProgress(gameId) {
    const user = auth.currentUser;
    if (!user) {
        return Promise.resolve(null);
    }
    
    return db.collection('users').doc(user.uid).collection('progress').doc(gameId).get()
        .then((doc) => {
            if (doc.exists) {
                return doc.data();
            } else {
                return null;
            }
        })
        .catch((error) => {
            console.error('Error getting progress:', error);
            return null;
        });
}

// Analytics functions (placeholder for future implementation)
function trackGamePlay(gameId, gameTitle, category) {
    // TODO: Implement analytics tracking
    console.log('Game played:', { gameId, gameTitle, category });
    
    // Example: Save to Firestore analytics collection
    const user = auth.currentUser;
    if (user) {
        db.collection('analytics').add({
            userId: user.uid,
            action: 'game_played',
            gameId: gameId,
            gameTitle: gameTitle,
            category: category,
            timestamp: firebase.firestore.FieldValue.serverTimestamp()
        }).catch((error) => {
            console.error('Error tracking game play:', error);
        });
    }
}

function trackVideoWatch(videoId, videoTitle) {
    // TODO: Implement video analytics tracking
    console.log('Video watched:', { videoId, videoTitle });
    
    const user = auth.currentUser;
    if (user) {
        db.collection('analytics').add({
            userId: user.uid,
            action: 'video_watched',
            videoId: videoId,
            videoTitle: videoTitle,
            timestamp: firebase.firestore.FieldValue.serverTimestamp()
        }).catch((error) => {
            console.error('Error tracking video watch:', error);
        });
    }
}

// Export functions for use in other files
window.firebaseAuth = {
    signInWithEmail,
    signUpWithEmail,
    signInAnonymously,
    signOut,
    addToFavorites,
    removeFromFavorites,
    getUserFavorites,
    checkIfFavorite,
    saveGameProgress,
    getGameProgress,
    trackGamePlay,
    trackVideoWatch
};
