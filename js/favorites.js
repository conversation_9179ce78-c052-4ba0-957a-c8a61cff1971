// Favorites Page Specific JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initializeFavoritesPage();
});

function initializeFavoritesPage() {
    setupAuthStateListener();
    setupFilterButtons();
    setupLoginButtons();
}

// Auth State Management
function setupAuthStateListener() {
    if (typeof firebase !== 'undefined') {
        firebase.auth().onAuthStateChanged((user) => {
            if (user) {
                showFavoritesContent();
                loadUserFavorites();
            } else {
                showLoginRequired();
            }
        });
    }
}

function showLoginRequired() {
    const loginRequired = document.getElementById('loginRequired');
    const favoritesSection = document.getElementById('favoritesSection');
    
    if (loginRequired) loginRequired.style.display = 'block';
    if (favoritesSection) favoritesSection.style.display = 'none';
}

function showFavoritesContent() {
    const loginRequired = document.getElementById('loginRequired');
    const favoritesSection = document.getElementById('favoritesSection');
    
    if (loginRequired) loginRequired.style.display = 'none';
    if (favoritesSection) favoritesSection.style.display = 'block';
}

// Login Button Handlers
function setupLoginButtons() {
    const loginFromFavorites = document.getElementById('loginFromFavorites');
    const signupFromFavorites = document.getElementById('signupFromFavorites');
    
    if (loginFromFavorites) {
        loginFromFavorites.addEventListener('click', () => {
            // Trigger the main login modal
            const loginBtn = document.getElementById('loginBtn');
            if (loginBtn) loginBtn.click();
        });
    }
    
    if (signupFromFavorites) {
        signupFromFavorites.addEventListener('click', () => {
            // Trigger the main signup modal
            const signupBtn = document.getElementById('signupBtn');
            if (signupBtn) signupBtn.click();
        });
    }
}

// Filter Functionality
function setupFilterButtons() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            filterButtons.forEach(btn => btn.classList.remove('active'));
            
            // Add active class to clicked button
            this.classList.add('active');
            
            // Filter favorites
            const filter = this.dataset.filter;
            filterFavorites(filter);
        });
    });
}

function filterFavorites(filter) {
    const favoriteItems = document.querySelectorAll('.favorite-item');
    
    favoriteItems.forEach(item => {
        const itemType = item.classList.contains('game-item') ? 'games' : 'videos';
        
        if (filter === 'all' || filter === itemType) {
            item.style.display = 'block';
            // Re-trigger animation
            item.style.animation = 'none';
            item.offsetHeight; // Trigger reflow
            item.style.animation = 'fadeInUp 0.6s ease forwards';
        } else {
            item.style.display = 'none';
        }
    });
}

// Load User Favorites
function loadUserFavorites() {
    const favoritesContainer = document.getElementById('favoritesContainer');
    if (!favoritesContainer) return;
    
    // Show loading state
    favoritesContainer.innerHTML = `
        <div class="loading-favorites">
            <i class="fas fa-spinner fa-spin"></i>
            <h3>Loading your favorites...</h3>
            <p>Please wait while we fetch your saved content.</p>
        </div>
    `;
    
    // Load favorites from Firebase
    if (window.firebaseAuth && window.firebaseAuth.getUserFavorites) {
        window.firebaseAuth.getUserFavorites()
            .then((favorites) => {
                displayFavorites(favorites);
                updateFavoritesStats(favorites);
            })
            .catch((error) => {
                console.error('Error loading favorites:', error);
                showFavoritesError();
            });
    } else {
        // Fallback if Firebase is not available
        setTimeout(() => {
            displayFavorites([]);
        }, 1000);
    }
}

function displayFavorites(favorites) {
    const favoritesContainer = document.getElementById('favoritesContainer');
    if (!favoritesContainer) return;
    
    if (favorites.length === 0) {
        favoritesContainer.innerHTML = `
            <div class="no-favorites">
                <i class="fas fa-heart-broken"></i>
                <h3>No favorites yet!</h3>
                <p>Start exploring games and videos, then click the heart icon to add them to your favorites!</p>
                <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap; margin-top: 20px;">
                    <a href="games.html" class="btn btn-primary">
                        <i class="fas fa-gamepad"></i> Browse Games
                    </a>
                    <a href="videos.html" class="btn btn-secondary">
                        <i class="fas fa-play-circle"></i> Watch Videos
                    </a>
                </div>
            </div>
        `;
        return;
    }
    
    // Generate HTML for favorites
    const favoritesHTML = favorites.map((favorite, index) => {
        const isVideo = favorite.type === 'video';
        const itemClass = isVideo ? 'video-item' : 'game-item';
        const typeLabel = isVideo ? 'Video' : 'Game';
        const playIcon = isVideo ? 'fa-play' : 'fa-gamepad';
        const playText = isVideo ? 'Watch' : 'Play';
        
        return `
            <div class="favorite-item ${itemClass}" data-favorite-id="${favorite.id}" data-type="${favorite.type || 'game'}" style="animation-delay: ${index * 0.1}s;">
                <div class="favorite-thumbnail">
                    <img src="${favorite.thumbnail || 'https://via.placeholder.com/300x180/8ECAE6/023047?text=' + encodeURIComponent(favorite.title)}" alt="${favorite.title}">
                    <div class="favorite-overlay">
                        <button class="play-btn" onclick="playFavorite('${favorite.id}', '${favorite.type || 'game'}')">
                            <i class="fas ${playIcon}"></i> ${playText} Now
                        </button>
                    </div>
                </div>
                <div class="favorite-info">
                    <div class="favorite-header">
                        <h3 class="favorite-title">${favorite.title}</h3>
                        <button class="remove-favorite" onclick="removeFavorite('${favorite.id}')" title="Remove from favorites">
                            <i class="fas fa-heart"></i>
                        </button>
                    </div>
                    <p class="favorite-description">${favorite.description}</p>
                    <div class="favorite-meta">
                        <span class="favorite-badge type-badge">${typeLabel}</span>
                        ${favorite.category ? `<span class="favorite-badge category-badge">${favorite.category}</span>` : ''}
                        ${favorite.ageRange ? `<span class="favorite-badge age-badge">Ages ${favorite.ageRange}</span>` : ''}
                    </div>
                </div>
            </div>
        `;
    }).join('');
    
    favoritesContainer.innerHTML = favoritesHTML;
}

function showFavoritesError() {
    const favoritesContainer = document.getElementById('favoritesContainer');
    if (!favoritesContainer) return;
    
    favoritesContainer.innerHTML = `
        <div class="error-message">
            <i class="fas fa-exclamation-triangle"></i>
            <h3>Oops! Something went wrong</h3>
            <p>We couldn't load your favorites right now. Please try refreshing the page.</p>
            <button class="btn btn-primary" onclick="loadUserFavorites()">
                <i class="fas fa-refresh"></i> Try Again
            </button>
        </div>
    `;
}

// Update Favorites Stats
function updateFavoritesStats(favorites) {
    const gamesCount = favorites.filter(f => f.type !== 'video').length;
    const videosCount = favorites.filter(f => f.type === 'video').length;
    
    const gamesCountEl = document.getElementById('gamesCount');
    const videosCountEl = document.getElementById('videosCount');
    const lastActivityEl = document.getElementById('lastActivity');
    
    if (gamesCountEl) {
        animateNumber(gamesCountEl, gamesCount);
    }
    
    if (videosCountEl) {
        animateNumber(videosCountEl, videosCount);
    }
    
    if (lastActivityEl && favorites.length > 0) {
        // Find the most recent favorite
        const mostRecent = favorites.reduce((latest, current) => {
            const currentDate = current.addedAt ? current.addedAt.toDate() : new Date();
            const latestDate = latest.addedAt ? latest.addedAt.toDate() : new Date(0);
            return currentDate > latestDate ? current : latest;
        });
        
        if (mostRecent.addedAt) {
            const lastActivity = formatRelativeTime(mostRecent.addedAt.toDate());
            lastActivityEl.textContent = lastActivity;
        }
    }
}

function animateNumber(element, targetNumber) {
    const startNumber = 0;
    const duration = 1000; // 1 second
    const startTime = performance.now();
    
    function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const currentNumber = Math.floor(startNumber + (targetNumber - startNumber) * progress);
        element.textContent = currentNumber;
        
        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        }
    }
    
    requestAnimationFrame(updateNumber);
}

function formatRelativeTime(date) {
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);
    
    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} min ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)} days ago`;
    
    return date.toLocaleDateString();
}

// Favorite Actions
function playFavorite(favoriteId, type) {
    if (type === 'video') {
        // Redirect to videos page or open video
        window.location.href = 'videos.html';
    } else {
        // Redirect to games page or open game
        window.location.href = 'games.html';
    }
    
    // Track the interaction
    if (window.firebaseAuth) {
        if (type === 'video') {
            window.firebaseAuth.trackVideoWatch(favoriteId, 'Favorite Video');
        } else {
            window.firebaseAuth.trackGamePlay(favoriteId, 'Favorite Game', 'favorites');
        }
    }
}

function removeFavorite(favoriteId) {
    // Show confirmation
    if (!confirm('Are you sure you want to remove this from your favorites?')) {
        return;
    }
    
    // Remove from Firebase
    if (window.firebaseAuth && window.firebaseAuth.removeFromFavorites) {
        window.firebaseAuth.removeFromFavorites(favoriteId)
            .then(() => {
                // Remove from UI
                const favoriteElement = document.querySelector(`[data-favorite-id="${favoriteId}"]`);
                if (favoriteElement) {
                    favoriteElement.style.animation = 'fadeOut 0.3s ease forwards';
                    setTimeout(() => {
                        favoriteElement.remove();
                        
                        // Check if no favorites left
                        const remainingFavorites = document.querySelectorAll('.favorite-item');
                        if (remainingFavorites.length === 0) {
                            displayFavorites([]);
                        } else {
                            // Update stats
                            loadUserFavorites();
                        }
                    }, 300);
                }
            })
            .catch((error) => {
                console.error('Error removing favorite:', error);
                alert('Failed to remove favorite. Please try again.');
            });
    }
}

// CSS for fade out animation
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeOut {
        to {
            opacity: 0;
            transform: translateY(-20px) scale(0.9);
        }
    }
`;
document.head.appendChild(style);

// Export functions for global use
window.favoritesPage = {
    loadUserFavorites,
    playFavorite,
    removeFavorite,
    filterFavorites
};
