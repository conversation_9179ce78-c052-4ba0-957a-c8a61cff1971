// Videos Page Specific JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initializeVideosPage();
});

function initializeVideosPage() {
    setupVideoFilters();
    setupVideoTracking();
    setupVideoInteractions();
}

// Video Filter Functionality
function setupVideoFilters() {
    const topicFilter = document.getElementById('topicFilter');
    if (topicFilter) {
        topicFilter.addEventListener('change', filterVideosByTopic);
    }
}

function filterVideosByTopic() {
    const selectedTopic = document.getElementById('topicFilter').value;
    const videoCards = document.querySelectorAll('.video-card');
    
    videoCards.forEach(card => {
        const videoTopic = card.dataset.topic;
        if (selectedTopic === 'all' || videoTopic === selectedTopic) {
            card.style.display = 'block';
            // Re-trigger animation
            card.style.animation = 'none';
            card.offsetHeight; // Trigger reflow
            card.style.animation = 'fadeInUp 0.6s ease forwards';
        } else {
            card.style.display = 'none';
        }
    });
    
    // Update page title based on filter
    updatePageTitle(selectedTopic);
}

function updatePageTitle(topic) {
    const pageTitle = document.querySelector('.page-title');
    if (pageTitle) {
        const topicNames = {
            'all': 'Educational Videos',
            'songs': 'Songs & Music Videos',
            'learning': 'Learning & Education Videos',
            'stories': 'Stories & Tales Videos'
        };
        
        const titleText = topicNames[topic] || 'Educational Videos';
        pageTitle.innerHTML = `<i class="fas fa-play-circle"></i> ${titleText}`;
    }
}

// Video Tracking and Analytics
function setupVideoTracking() {
    const videoCards = document.querySelectorAll('.video-card');
    
    videoCards.forEach(card => {
        const iframe = card.querySelector('iframe');
        const videoId = card.dataset.videoId;
        const videoTitle = card.querySelector('.video-title').textContent;
        
        // Track when video card is clicked
        card.addEventListener('click', function() {
            trackVideoInteraction(videoId, videoTitle, 'card_clicked');
        });
        
        // Track when iframe is interacted with (approximate play detection)
        if (iframe) {
            iframe.addEventListener('load', function() {
                // Set up message listener for YouTube iframe API (if available)
                setupYouTubeTracking(videoId, videoTitle);
            });
        }
    });
}

function trackVideoInteraction(videoId, videoTitle, action) {
    // Use the Firebase tracking function
    if (window.firebaseAuth && window.firebaseAuth.trackVideoWatch) {
        window.firebaseAuth.trackVideoWatch(videoId, videoTitle);
    }
    
    // Additional analytics tracking
    console.log('Video interaction:', {
        videoId: videoId,
        title: videoTitle,
        action: action,
        timestamp: new Date().toISOString()
    });
    
    // You can add more analytics services here
    // Example: Google Analytics, custom analytics, etc.
}

function setupYouTubeTracking(videoId, videoTitle) {
    // This is a placeholder for YouTube API integration
    // In a real implementation, you would use the YouTube IFrame Player API
    // to track actual play events, pause events, etc.
    
    // Example of what you could track:
    // - Video started
    // - Video paused
    // - Video completed
    // - Video progress (25%, 50%, 75%, 100%)
    
    console.log('YouTube tracking setup for:', videoTitle);
}

// Video Interaction Enhancements
function setupVideoInteractions() {
    const videoCards = document.querySelectorAll('.video-card');
    
    videoCards.forEach(card => {
        // Add hover effects and interactions
        card.addEventListener('mouseenter', function() {
            this.classList.add('hovered');
        });
        
        card.addEventListener('mouseleave', function() {
            this.classList.remove('hovered');
        });
        
        // Add keyboard navigation support
        card.setAttribute('tabindex', '0');
        card.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.click();
            }
        });
    });
}

// Utility function to format video duration
function formatDuration(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

// Function to add new video cards dynamically (for future use)
function addVideoCard(videoData) {
    const videosGrid = document.getElementById('videosGrid');
    if (!videosGrid) return;
    
    const videoCard = document.createElement('div');
    videoCard.className = 'video-card';
    videoCard.dataset.videoId = videoData.id;
    videoCard.dataset.topic = videoData.topic;
    
    videoCard.innerHTML = `
        <div class="video-thumbnail">
            <iframe 
                src="https://www.youtube.com/embed/${videoData.id}" 
                title="${videoData.title}" 
                frameborder="0" 
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                allowfullscreen>
            </iframe>
        </div>
        <div class="video-info">
            <h3 class="video-title">${videoData.title}</h3>
            <p class="video-description">${videoData.description}</p>
            <div class="video-meta">
                <span class="duration-badge">${videoData.duration}</span>
                <span class="topic-badge">${videoData.topic}</span>
            </div>
        </div>
    `;
    
    videosGrid.appendChild(videoCard);
    
    // Set up tracking for the new video card
    setupVideoTrackingForCard(videoCard);
}

function setupVideoTrackingForCard(card) {
    const videoId = card.dataset.videoId;
    const videoTitle = card.querySelector('.video-title').textContent;
    
    card.addEventListener('click', function() {
        trackVideoInteraction(videoId, videoTitle, 'card_clicked');
    });
}

// Function to load videos from an API or database (placeholder)
function loadVideosFromAPI() {
    // This is a placeholder for loading videos from a backend API
    // In a real implementation, you would fetch video data from your server
    
    console.log('Loading videos from API...');
    
    // Example of how you might structure video data:
    const exampleVideoData = {
        id: 'dQw4w9WgXcQ',
        title: 'Example Video Title',
        description: 'Example video description',
        duration: '3:45',
        topic: 'songs',
        thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg'
    };
    
    // You would then call addVideoCard(exampleVideoData) for each video
}

// Function to handle video search (for future implementation)
function searchVideos(query) {
    const videoCards = document.querySelectorAll('.video-card');
    const searchQuery = query.toLowerCase();
    
    videoCards.forEach(card => {
        const title = card.querySelector('.video-title').textContent.toLowerCase();
        const description = card.querySelector('.video-description').textContent.toLowerCase();
        
        if (title.includes(searchQuery) || description.includes(searchQuery)) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

// Function to handle video favorites (integration with main favorites system)
function toggleVideoFavorite(videoId, videoData) {
    // This would integrate with the main favorites system
    // Similar to how game favorites work
    
    if (window.firebaseAuth) {
        // Check if video is already favorited
        window.firebaseAuth.checkIfFavorite(`video_${videoId}`)
            .then(isFavorite => {
                if (isFavorite) {
                    return window.firebaseAuth.removeFromFavorites(`video_${videoId}`);
                } else {
                    return window.firebaseAuth.addToFavorites(`video_${videoId}`, {
                        ...videoData,
                        type: 'video'
                    });
                }
            })
            .then(() => {
                console.log('Video favorite status updated');
            })
            .catch(error => {
                console.error('Error updating video favorite:', error);
            });
    }
}

// Export functions for use in other files
window.videosPage = {
    filterVideosByTopic,
    trackVideoInteraction,
    addVideoCard,
    searchVideos,
    toggleVideoFavorite
};
