<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fun Learning Games - LSTBook</title>
    <meta name="description" content="Play educational games for kids aged 4-10. Learn animals, math, letters, and more through fun interactive games!">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Fredoka:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/games.css">
</head>
<body>
    <!-- Top Bar -->
    <div class="top-bar">
        <div class="container">
            <div class="top-bar-content">
                <div class="welcome-text">
                    <i class="fas fa-gamepad"></i>
                    Ready to Play? Choose Your Adventure!
                </div>
                <div class="auth-buttons">
                    <button class="btn-login" id="loginBtn">
                        <i class="fas fa-user"></i> Login
                    </button>
                    <button class="btn-signup" id="signupBtn">
                        <i class="fas fa-user-plus"></i> Sign Up
                    </button>
                    <div class="user-menu" id="userMenu" style="display: none;">
                        <span class="user-name" id="userName">Welcome!</span>
                        <button class="btn-logout" id="logoutBtn">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <h1><a href="index.html"><i class="fas fa-book-open"></i> LSTBook</a></h1>
                    <p>Learning • Stories • Together</p>
                </div>
                <nav class="main-nav">
                    <ul class="nav-list">
                        <li><a href="index.html" class="nav-link">
                            <i class="fas fa-home"></i> Home
                        </a></li>
                        <li class="has-mega-menu">
                            <a href="games.html" class="nav-link active">
                                <i class="fas fa-gamepad"></i> Games
                                <i class="fas fa-chevron-down"></i>
                            </a>
                            <div class="mega-menu">
                                <div class="mega-menu-content">
                                    <div class="mega-menu-grid">
                                        <a href="games.html?category=animals" class="mega-menu-item">
                                            <i class="fas fa-paw"></i>
                                            <span>Animals</span>
                                        </a>
                                        <a href="games.html?category=math" class="mega-menu-item">
                                            <i class="fas fa-calculator"></i>
                                            <span>Numbers & Math</span>
                                        </a>
                                        <a href="games.html?category=letters" class="mega-menu-item">
                                            <i class="fas fa-font"></i>
                                            <span>Letters & Words</span>
                                        </a>
                                        <a href="games.html?category=shapes" class="mega-menu-item">
                                            <i class="fas fa-shapes"></i>
                                            <span>Shapes & Colors</span>
                                        </a>
                                        <a href="games.html?category=logic" class="mega-menu-item">
                                            <i class="fas fa-puzzle-piece"></i>
                                            <span>Logic & Puzzles</span>
                                        </a>
                                        <a href="games.html?category=memory" class="mega-menu-item">
                                            <i class="fas fa-brain"></i>
                                            <span>Memory Games</span>
                                        </a>
                                        <a href="games.html?category=music" class="mega-menu-item">
                                            <i class="fas fa-music"></i>
                                            <span>Music & Sounds</span>
                                        </a>
                                        <a href="games.html?category=stories" class="mega-menu-item">
                                            <i class="fas fa-book"></i>
                                            <span>Interactive Stories</span>
                                        </a>
                                        <a href="games.html?category=coding" class="mega-menu-item">
                                            <i class="fas fa-code"></i>
                                            <span>Learn to Code</span>
                                        </a>
                                        <a href="games.html?category=art" class="mega-menu-item">
                                            <i class="fas fa-palette"></i>
                                            <span>Creativity & Art</span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li><a href="videos.html" class="nav-link">
                            <i class="fas fa-play-circle"></i> Videos
                        </a></li>
                        <li><a href="explore.html" class="nav-link">
                            <i class="fas fa-compass"></i> Explore
                        </a></li>
                        <li><a href="favorites.html" class="nav-link">
                            <i class="fas fa-heart"></i> Favorites
                        </a></li>
                        <li><a href="about.html" class="nav-link">
                            <i class="fas fa-info-circle"></i> About
                        </a></li>
                        <li><a href="contact.html" class="nav-link">
                            <i class="fas fa-envelope"></i> Contact
                        </a></li>
                    </ul>
                    <div class="mobile-menu-toggle" id="mobileMenuToggle">
                        <i class="fas fa-bars"></i>
                    </div>
                </nav>
            </div>
        </div>
    </header>

    <!-- Page Header with Integrated Filters -->
    <section class="page-header">
        <div class="container">
            <div class="page-header-content">
                <h1 class="page-title">
                    <i class="fas fa-gamepad"></i> Fun Learning Games
                </h1>
                <p class="page-subtitle">Choose from over 30 educational games designed to make learning fun!</p>

                <!-- Interactive Filter Tags -->
                <div class="filter-tags-container">
                    <!-- Age Filter -->
                    <div class="filter-row">
                        <div class="filter-tags">
                            <button class="filter-tag filter-label-tag active" data-filter="age" data-value="all">
                                <i class="fas fa-child"></i> Age
                            </button>
                            <button class="filter-tag" data-filter="age" data-value="4-5">4-5</button>
                            <button class="filter-tag" data-filter="age" data-value="6-7">6-7</button>
                            <button class="filter-tag" data-filter="age" data-value="8-10">8-10</button>
                        </div>
                    </div>

                    <!-- Difficulty Filter -->
                    <div class="filter-row">
                        <div class="filter-tags">
                            <button class="filter-tag filter-label-tag active" data-filter="difficulty" data-value="all">
                                <i class="fas fa-star"></i> Difficulty
                            </button>
                            <button class="filter-tag" data-filter="difficulty" data-value="easy">Easy</button>
                            <button class="filter-tag" data-filter="difficulty" data-value="medium">Medium</button>
                            <button class="filter-tag" data-filter="difficulty" data-value="hard">Hard</button>
                        </div>
                    </div>

                    <!-- Category Filter -->
                    <div class="filter-row">
                        <div class="filter-tags">
                            <button class="filter-tag filter-label-tag active" data-filter="category" data-value="all">
                                <i class="fas fa-tags"></i> Category
                            </button>
                            <button class="filter-tag" data-filter="category" data-value="animals">
                                <i class="fas fa-paw"></i> Animals
                            </button>
                            <button class="filter-tag" data-filter="category" data-value="math">
                                <i class="fas fa-calculator"></i> Math
                            </button>
                            <button class="filter-tag" data-filter="category" data-value="letters">
                                <i class="fas fa-font"></i> Letters
                            </button>
                            <button class="filter-tag" data-filter="category" data-value="shapes">
                                <i class="fas fa-shapes"></i> Shapes
                            </button>
                            <button class="filter-tag" data-filter="category" data-value="logic">
                                <i class="fas fa-puzzle-piece"></i> Logic
                            </button>
                            <button class="filter-tag" data-filter="category" data-value="memory">
                                <i class="fas fa-brain"></i> Memory
                            </button>
                            <button class="filter-tag" data-filter="category" data-value="music">
                                <i class="fas fa-music"></i> Music
                            </button>
                            <button class="filter-tag" data-filter="category" data-value="stories">
                                <i class="fas fa-book"></i> Stories
                            </button>
                            <button class="filter-tag" data-filter="category" data-value="coding">
                                <i class="fas fa-code"></i> Coding
                            </button>
                            <button class="filter-tag" data-filter="category" data-value="art">
                                <i class="fas fa-palette"></i> Art
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Games Grid -->
    <section class="games-section">
        <div class="container">
            <div class="games-grid" id="gamesGrid">
                <!-- Animals Category Games -->
                <div class="game-card" data-game-id="animal-sounds" data-age-range="4-5" data-category="animals" data-difficulty="easy">
                    <div class="game-thumbnail">
                        <div class="game-placeholder" style="background: linear-gradient(135deg, #FF6B6B, #FFB6C1);">
                            <i class="fas fa-paw"></i>
                            <span>Animal Sounds</span>
                        </div>
                        <div class="game-overlay">
                            <button class="btn btn-play">
                                <i class="fas fa-play"></i> Play Now
                            </button>
                        </div>
                    </div>
                    <div class="game-info">
                        <div class="game-header">
                            <h3 class="game-title">Animal Sounds</h3>
                            <button class="favorite-btn" title="Add to Favorites">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                        <p class="game-description">Learn about different animals and the sounds they make!</p>
                        <div class="game-meta">
                            <span class="age-badge">Ages 4-5</span>
                            <span class="category-badge">Animals</span>
                        </div>
                    </div>
                </div>

                <div class="game-card" data-game-id="zoo-adventure" data-age-range="6-7" data-category="animals" data-difficulty="medium">
                    <div class="game-thumbnail">
                        <div class="game-placeholder" style="background: linear-gradient(135deg, #4ECDC4, #44A08D);">
                            <i class="fas fa-hippo"></i>
                            <span>Zoo Adventure</span>
                        </div>
                        <div class="game-overlay">
                            <button class="btn btn-play">
                                <i class="fas fa-play"></i> Play Now
                            </button>
                        </div>
                    </div>
                    <div class="game-info">
                        <div class="game-header">
                            <h3 class="game-title">Zoo Adventure</h3>
                            <button class="favorite-btn" title="Add to Favorites">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                        <p class="game-description">Explore the zoo and learn amazing facts about animals!</p>
                        <div class="game-meta">
                            <span class="age-badge">Ages 6-7</span>
                            <span class="category-badge">Animals</span>
                        </div>
                    </div>
                </div>

                <div class="game-card" data-game-id="animal-habitats" data-age-range="8-10" data-category="animals" data-difficulty="hard">
                    <div class="game-thumbnail">
                        <div class="game-placeholder" style="background: linear-gradient(135deg, #96CEB4, #FFEAA7);">
                            <i class="fas fa-tree"></i>
                            <span>Animal Habitats</span>
                        </div>
                        <div class="game-overlay">
                            <button class="btn btn-play">
                                <i class="fas fa-play"></i> Play Now
                            </button>
                        </div>
                    </div>
                    <div class="game-info">
                        <div class="game-header">
                            <h3 class="game-title">Animal Habitats</h3>
                            <button class="favorite-btn" title="Add to Favorites">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                        <p class="game-description">Match animals with their natural habitats around the world!</p>
                        <div class="game-meta">
                            <span class="age-badge">Ages 8-10</span>
                            <span class="category-badge">Animals</span>
                        </div>
                    </div>
                </div>

                <!-- Math Category Games -->
                <div class="game-card" data-game-id="counting-fun" data-age-range="4-5" data-category="math" data-difficulty="easy">
                    <div class="game-thumbnail">
                        <div class="game-placeholder" style="background: linear-gradient(135deg, #45B7D1, #96CEB4);">
                            <i class="fas fa-calculator"></i>
                            <span>Counting Fun</span>
                        </div>
                        <div class="game-overlay">
                            <button class="btn btn-play">
                                <i class="fas fa-play"></i> Play Now
                            </button>
                        </div>
                    </div>
                    <div class="game-info">
                        <div class="game-header">
                            <h3 class="game-title">Counting Fun</h3>
                            <button class="favorite-btn" title="Add to Favorites">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                        <p class="game-description">Learn to count from 1 to 10 with colorful objects!</p>
                        <div class="game-meta">
                            <span class="age-badge">Ages 4-5</span>
                            <span class="category-badge">Math</span>
                        </div>
                    </div>
                </div>

                <div class="game-card" data-game-id="addition-adventure" data-age-range="6-7" data-category="math">
                    <div class="game-thumbnail">
                        <div class="game-placeholder" style="background: linear-gradient(135deg, #FFEAA7, #FDCB6E);">
                            <i class="fas fa-plus"></i>
                            <span>Addition Adventure</span>
                        </div>
                        <div class="game-overlay">
                            <button class="btn btn-play">
                                <i class="fas fa-play"></i> Play Now
                            </button>
                        </div>
                    </div>
                    <div class="game-info">
                        <div class="game-header">
                            <h3 class="game-title">Addition Adventure</h3>
                            <button class="favorite-btn" title="Add to Favorites">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                        <p class="game-description">Practice addition with fun visual problems!</p>
                        <div class="game-meta">
                            <span class="age-badge">Ages 6-7</span>
                            <span class="category-badge">Math</span>
                        </div>
                    </div>
                </div>

                <div class="game-card" data-game-id="multiplication-master" data-age-range="8-10" data-category="math">
                    <div class="game-thumbnail">
                        <div class="game-placeholder" style="background: linear-gradient(135deg, #8ECAE6, #219EBC);">
                            <i class="fas fa-times"></i>
                            <span>Multiplication Master</span>
                        </div>
                        <div class="game-overlay">
                            <button class="btn btn-play">
                                <i class="fas fa-play"></i> Play Now
                            </button>
                        </div>
                    </div>
                    <div class="game-info">
                        <div class="game-header">
                            <h3 class="game-title">Multiplication Master</h3>
                            <button class="favorite-btn" title="Add to Favorites">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                        <p class="game-description">Master multiplication tables through interactive challenges!</p>
                        <div class="game-meta">
                            <span class="age-badge">Ages 8-10</span>
                            <span class="category-badge">Math</span>
                        </div>
                    </div>
                </div>

                <!-- Letters & Words Category Games -->
                <div class="game-card" data-game-id="alphabet-adventure" data-age-range="4-5" data-category="letters">
                    <div class="game-thumbnail">
                        <div class="game-placeholder" style="background: linear-gradient(135deg, #DDA0DD, #BB8FCE);">
                            <i class="fas fa-font"></i>
                            <span>Alphabet Adventure</span>
                        </div>
                        <div class="game-overlay">
                            <button class="btn btn-play">
                                <i class="fas fa-play"></i> Play Now
                            </button>
                        </div>
                    </div>
                    <div class="game-info">
                        <div class="game-header">
                            <h3 class="game-title">Alphabet Adventure</h3>
                            <button class="favorite-btn" title="Add to Favorites">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                        <p class="game-description">Learn the alphabet with fun characters and sounds!</p>
                        <div class="game-meta">
                            <span class="age-badge">Ages 4-5</span>
                            <span class="category-badge">Letters</span>
                        </div>
                    </div>
                </div>

                <div class="game-card" data-game-id="word-builder" data-age-range="6-7" data-category="letters">
                    <div class="game-thumbnail">
                        <div class="game-placeholder" style="background: linear-gradient(135deg, #219EBC, #8ECAE6);">
                            <i class="fas fa-spell-check"></i>
                            <span>Word Builder</span>
                        </div>
                        <div class="game-overlay">
                            <button class="btn btn-play">
                                <i class="fas fa-play"></i> Play Now
                            </button>
                        </div>
                    </div>
                    <div class="game-info">
                        <div class="game-header">
                            <h3 class="game-title">Word Builder</h3>
                            <button class="favorite-btn" title="Add to Favorites">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                        <p class="game-description">Build words by combining letters and sounds!</p>
                        <div class="game-meta">
                            <span class="age-badge">Ages 6-7</span>
                            <span class="category-badge">Letters</span>
                        </div>
                    </div>
                </div>

                <div class="game-card" data-game-id="spelling-bee" data-age-range="8-10" data-category="letters">
                    <div class="game-thumbnail">
                        <div class="game-placeholder" style="background: linear-gradient(135deg, #FB8500, #FFB703);">
                            <i class="fas fa-bee"></i>
                            <span>Spelling Bee</span>
                        </div>
                        <div class="game-overlay">
                            <button class="btn btn-play">
                                <i class="fas fa-play"></i> Play Now
                            </button>
                        </div>
                    </div>
                    <div class="game-info">
                        <div class="game-header">
                            <h3 class="game-title">Spelling Bee</h3>
                            <button class="favorite-btn" title="Add to Favorites">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                        <p class="game-description">Test your spelling skills with challenging words!</p>
                        <div class="game-meta">
                            <span class="age-badge">Ages 8-10</span>
                            <span class="category-badge">Letters</span>
                        </div>
                    </div>
                </div>

                <!-- Shapes & Colors Category Games -->
                <div class="game-card" data-game-id="shape-sorter" data-age-range="4-5" data-category="shapes">
                    <div class="game-thumbnail">
                        <div class="game-placeholder" style="background: linear-gradient(135deg, #FFDDD2, #FFB5A7);">
                            <i class="fas fa-shapes"></i>
                            <span>Shape Sorter</span>
                        </div>
                        <div class="game-overlay">
                            <button class="btn btn-play">
                                <i class="fas fa-play"></i> Play Now
                            </button>
                        </div>
                    </div>
                    <div class="game-info">
                        <div class="game-header">
                            <h3 class="game-title">Shape Sorter</h3>
                            <button class="favorite-btn" title="Add to Favorites">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                        <p class="game-description">Learn basic shapes by sorting them into the right places!</p>
                        <div class="game-meta">
                            <span class="age-badge">Ages 4-5</span>
                            <span class="category-badge">Shapes</span>
                        </div>
                    </div>
                </div>

                <div class="game-card" data-game-id="color-mixer" data-age-range="6-7" data-category="shapes">
                    <div class="game-thumbnail">
                        <div class="game-placeholder" style="background: linear-gradient(135deg, #8ECAE6, #219EBC);">
                            <i class="fas fa-palette"></i>
                            <span>Color Mixer</span>
                        </div>
                        <div class="game-overlay">
                            <button class="btn btn-play">
                                <i class="fas fa-play"></i> Play Now
                            </button>
                        </div>
                    </div>
                    <div class="game-info">
                        <div class="game-header">
                            <h3 class="game-title">Color Mixer</h3>
                            <button class="favorite-btn" title="Add to Favorites">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                        <p class="game-description">Mix primary colors to create new colors!</p>
                        <div class="game-meta">
                            <span class="age-badge">Ages 6-7</span>
                            <span class="category-badge">Shapes</span>
                        </div>
                    </div>
                </div>

                <div class="game-card" data-game-id="pattern-master" data-age-range="8-10" data-category="shapes">
                    <div class="game-thumbnail">
                        <img src="https://via.placeholder.com/300x200/FFB703/023047?text=Pattern+Master" alt="Pattern Master Game">
                        <div class="game-overlay">
                            <button class="btn btn-play">
                                <i class="fas fa-play"></i> Play Now
                            </button>
                        </div>
                    </div>
                    <div class="game-info">
                        <div class="game-header">
                            <h3 class="game-title">Pattern Master</h3>
                            <button class="favorite-btn" title="Add to Favorites">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                        <p class="game-description">Complete complex patterns using shapes and colors!</p>
                        <div class="game-meta">
                            <span class="age-badge">Ages 8-10</span>
                            <span class="category-badge">Shapes</span>
                        </div>
                    </div>
                </div>

                <!-- Logic & Puzzles Category Games -->
                <div class="game-card" data-game-id="simple-puzzles" data-age-range="4-5" data-category="logic">
                    <div class="game-thumbnail">
                        <img src="https://via.placeholder.com/300x200/219EBC/FFFFFF?text=Simple+Puzzles" alt="Simple Puzzles Game">
                        <div class="game-overlay">
                            <button class="btn btn-play">
                                <i class="fas fa-play"></i> Play Now
                            </button>
                        </div>
                    </div>
                    <div class="game-info">
                        <div class="game-header">
                            <h3 class="game-title">Simple Puzzles</h3>
                            <button class="favorite-btn" title="Add to Favorites">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                        <p class="game-description">Solve easy jigsaw puzzles with big, colorful pieces!</p>
                        <div class="game-meta">
                            <span class="age-badge">Ages 4-5</span>
                            <span class="category-badge">Logic</span>
                        </div>
                    </div>
                </div>

                <div class="game-card" data-game-id="brain-teasers" data-age-range="6-7" data-category="logic">
                    <div class="game-thumbnail">
                        <img src="https://via.placeholder.com/300x200/FB8500/FFFFFF?text=Brain+Teasers" alt="Brain Teasers Game">
                        <div class="game-overlay">
                            <button class="btn btn-play">
                                <i class="fas fa-play"></i> Play Now
                            </button>
                        </div>
                    </div>
                    <div class="game-info">
                        <div class="game-header">
                            <h3 class="game-title">Brain Teasers</h3>
                            <button class="favorite-btn" title="Add to Favorites">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                        <p class="game-description">Challenge your mind with fun logic problems!</p>
                        <div class="game-meta">
                            <span class="age-badge">Ages 6-7</span>
                            <span class="category-badge">Logic</span>
                        </div>
                    </div>
                </div>

                <div class="game-card" data-game-id="logic-master" data-age-range="8-10" data-category="logic">
                    <div class="game-thumbnail">
                        <img src="https://via.placeholder.com/300x200/FFDDD2/023047?text=Logic+Master" alt="Logic Master Game">
                        <div class="game-overlay">
                            <button class="btn btn-play">
                                <i class="fas fa-play"></i> Play Now
                            </button>
                        </div>
                    </div>
                    <div class="game-info">
                        <div class="game-header">
                            <h3 class="game-title">Logic Master</h3>
                            <button class="favorite-btn" title="Add to Favorites">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                        <p class="game-description">Solve advanced puzzles and logical sequences!</p>
                        <div class="game-meta">
                            <span class="age-badge">Ages 8-10</span>
                            <span class="category-badge">Logic</span>
                        </div>
                    </div>
                </div>

                <!-- Memory Games Category -->
                <div class="game-card" data-game-id="memory-match" data-age-range="4-5" data-category="memory">
                    <div class="game-thumbnail">
                        <img src="https://via.placeholder.com/300x200/8ECAE6/023047?text=Memory+Match" alt="Memory Match Game">
                        <div class="game-overlay">
                            <button class="btn btn-play">
                                <i class="fas fa-play"></i> Play Now
                            </button>
                        </div>
                    </div>
                    <div class="game-info">
                        <div class="game-header">
                            <h3 class="game-title">Memory Match</h3>
                            <button class="favorite-btn" title="Add to Favorites">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                        <p class="game-description">Match pairs of cards to improve your memory!</p>
                        <div class="game-meta">
                            <span class="age-badge">Ages 4-5</span>
                            <span class="category-badge">Memory</span>
                        </div>
                    </div>
                </div>

                <div class="game-card" data-game-id="sequence-game" data-age-range="6-7" data-category="memory">
                    <div class="game-thumbnail">
                        <img src="https://via.placeholder.com/300x200/FFB703/023047?text=Sequence+Game" alt="Sequence Game">
                        <div class="game-overlay">
                            <button class="btn btn-play">
                                <i class="fas fa-play"></i> Play Now
                            </button>
                        </div>
                    </div>
                    <div class="game-info">
                        <div class="game-header">
                            <h3 class="game-title">Sequence Game</h3>
                            <button class="favorite-btn" title="Add to Favorites">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                        <p class="game-description">Remember and repeat colorful sequences!</p>
                        <div class="game-meta">
                            <span class="age-badge">Ages 6-7</span>
                            <span class="category-badge">Memory</span>
                        </div>
                    </div>
                </div>

                <div class="game-card" data-game-id="memory-palace" data-age-range="8-10" data-category="memory">
                    <div class="game-thumbnail">
                        <img src="https://via.placeholder.com/300x200/219EBC/FFFFFF?text=Memory+Palace" alt="Memory Palace Game">
                        <div class="game-overlay">
                            <button class="btn btn-play">
                                <i class="fas fa-play"></i> Play Now
                            </button>
                        </div>
                    </div>
                    <div class="game-info">
                        <div class="game-header">
                            <h3 class="game-title">Memory Palace</h3>
                            <button class="favorite-btn" title="Add to Favorites">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                        <p class="game-description">Build your memory skills with complex challenges!</p>
                        <div class="game-meta">
                            <span class="age-badge">Ages 8-10</span>
                            <span class="category-badge">Memory</span>
                        </div>
                    </div>
                </div>

                <!-- Music & Sounds Category Games -->
                <div class="game-card" data-game-id="musical-notes" data-age-range="4-5" data-category="music">
                    <div class="game-thumbnail">
                        <img src="https://via.placeholder.com/300x200/FB8500/FFFFFF?text=Musical+Notes" alt="Musical Notes Game">
                        <div class="game-overlay">
                            <button class="btn btn-play">
                                <i class="fas fa-play"></i> Play Now
                            </button>
                        </div>
                    </div>
                    <div class="game-info">
                        <div class="game-header">
                            <h3 class="game-title">Musical Notes</h3>
                            <button class="favorite-btn" title="Add to Favorites">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                        <p class="game-description">Learn about different musical sounds and instruments!</p>
                        <div class="game-meta">
                            <span class="age-badge">Ages 4-5</span>
                            <span class="category-badge">Music</span>
                        </div>
                    </div>
                </div>

                <div class="game-card" data-game-id="rhythm-maker" data-age-range="6-7" data-category="music">
                    <div class="game-thumbnail">
                        <img src="https://via.placeholder.com/300x200/FFDDD2/023047?text=Rhythm+Maker" alt="Rhythm Maker Game">
                        <div class="game-overlay">
                            <button class="btn btn-play">
                                <i class="fas fa-play"></i> Play Now
                            </button>
                        </div>
                    </div>
                    <div class="game-info">
                        <div class="game-header">
                            <h3 class="game-title">Rhythm Maker</h3>
                            <button class="favorite-btn" title="Add to Favorites">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                        <p class="game-description">Create your own beats and rhythms!</p>
                        <div class="game-meta">
                            <span class="age-badge">Ages 6-7</span>
                            <span class="category-badge">Music</span>
                        </div>
                    </div>
                </div>

                <div class="game-card" data-game-id="music-composer" data-age-range="8-10" data-category="music">
                    <div class="game-thumbnail">
                        <img src="https://via.placeholder.com/300x200/8ECAE6/023047?text=Music+Composer" alt="Music Composer Game">
                        <div class="game-overlay">
                            <button class="btn btn-play">
                                <i class="fas fa-play"></i> Play Now
                            </button>
                        </div>
                    </div>
                    <div class="game-info">
                        <div class="game-header">
                            <h3 class="game-title">Music Composer</h3>
                            <button class="favorite-btn" title="Add to Favorites">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                        <p class="game-description">Compose your own melodies and songs!</p>
                        <div class="game-meta">
                            <span class="age-badge">Ages 8-10</span>
                            <span class="category-badge">Music</span>
                        </div>
                    </div>
                </div>

                <!-- Interactive Stories Category Games -->
                <div class="game-card" data-game-id="story-builder" data-age-range="4-5" data-category="stories">
                    <div class="game-thumbnail">
                        <img src="https://via.placeholder.com/300x200/FFB703/023047?text=Story+Builder" alt="Story Builder Game">
                        <div class="game-overlay">
                            <button class="btn btn-play">
                                <i class="fas fa-play"></i> Play Now
                            </button>
                        </div>
                    </div>
                    <div class="game-info">
                        <div class="game-header">
                            <h3 class="game-title">Story Builder</h3>
                            <button class="favorite-btn" title="Add to Favorites">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                        <p class="game-description">Create your own stories with pictures and words!</p>
                        <div class="game-meta">
                            <span class="age-badge">Ages 4-5</span>
                            <span class="category-badge">Stories</span>
                        </div>
                    </div>
                </div>

                <div class="game-card" data-game-id="adventure-tales" data-age-range="6-7" data-category="stories">
                    <div class="game-thumbnail">
                        <img src="https://via.placeholder.com/300x200/219EBC/FFFFFF?text=Adventure+Tales" alt="Adventure Tales Game">
                        <div class="game-overlay">
                            <button class="btn btn-play">
                                <i class="fas fa-play"></i> Play Now
                            </button>
                        </div>
                    </div>
                    <div class="game-info">
                        <div class="game-header">
                            <h3 class="game-title">Adventure Tales</h3>
                            <button class="favorite-btn" title="Add to Favorites">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                        <p class="game-description">Choose your own adventure in interactive stories!</p>
                        <div class="game-meta">
                            <span class="age-badge">Ages 6-7</span>
                            <span class="category-badge">Stories</span>
                        </div>
                    </div>
                </div>

                <div class="game-card" data-game-id="story-master" data-age-range="8-10" data-category="stories">
                    <div class="game-thumbnail">
                        <img src="https://via.placeholder.com/300x200/FB8500/FFFFFF?text=Story+Master" alt="Story Master Game">
                        <div class="game-overlay">
                            <button class="btn btn-play">
                                <i class="fas fa-play"></i> Play Now
                            </button>
                        </div>
                    </div>
                    <div class="game-info">
                        <div class="game-header">
                            <h3 class="game-title">Story Master</h3>
                            <button class="favorite-btn" title="Add to Favorites">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                        <p class="game-description">Write and illustrate complex interactive stories!</p>
                        <div class="game-meta">
                            <span class="age-badge">Ages 8-10</span>
                            <span class="category-badge">Stories</span>
                        </div>
                    </div>
                </div>

                <!-- Learn to Code Category Games -->
                <div class="game-card" data-game-id="code-blocks" data-age-range="4-5" data-category="coding">
                    <div class="game-thumbnail">
                        <img src="https://via.placeholder.com/300x200/FFDDD2/023047?text=Code+Blocks" alt="Code Blocks Game">
                        <div class="game-overlay">
                            <button class="btn btn-play">
                                <i class="fas fa-play"></i> Play Now
                            </button>
                        </div>
                    </div>
                    <div class="game-info">
                        <div class="game-header">
                            <h3 class="game-title">Code Blocks</h3>
                            <button class="favorite-btn" title="Add to Favorites">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                        <p class="game-description">Learn basic coding concepts with colorful blocks!</p>
                        <div class="game-meta">
                            <span class="age-badge">Ages 4-5</span>
                            <span class="category-badge">Coding</span>
                        </div>
                    </div>
                </div>

                <div class="game-card" data-game-id="robot-commands" data-age-range="6-7" data-category="coding">
                    <div class="game-thumbnail">
                        <img src="https://via.placeholder.com/300x200/8ECAE6/023047?text=Robot+Commands" alt="Robot Commands Game">
                        <div class="game-overlay">
                            <button class="btn btn-play">
                                <i class="fas fa-play"></i> Play Now
                            </button>
                        </div>
                    </div>
                    <div class="game-info">
                        <div class="game-header">
                            <h3 class="game-title">Robot Commands</h3>
                            <button class="favorite-btn" title="Add to Favorites">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                        <p class="game-description">Program a robot to complete fun challenges!</p>
                        <div class="game-meta">
                            <span class="age-badge">Ages 6-7</span>
                            <span class="category-badge">Coding</span>
                        </div>
                    </div>
                </div>

                <div class="game-card" data-game-id="junior-programmer" data-age-range="8-10" data-category="coding">
                    <div class="game-thumbnail">
                        <img src="https://via.placeholder.com/300x200/FFB703/023047?text=Junior+Programmer" alt="Junior Programmer Game">
                        <div class="game-overlay">
                            <button class="btn btn-play">
                                <i class="fas fa-play"></i> Play Now
                            </button>
                        </div>
                    </div>
                    <div class="game-info">
                        <div class="game-header">
                            <h3 class="game-title">Junior Programmer</h3>
                            <button class="favorite-btn" title="Add to Favorites">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                        <p class="game-description">Create simple programs and animations!</p>
                        <div class="game-meta">
                            <span class="age-badge">Ages 8-10</span>
                            <span class="category-badge">Coding</span>
                        </div>
                    </div>
                </div>

                <!-- Creativity & Art Category Games -->
                <div class="game-card" data-game-id="digital-coloring" data-age-range="4-5" data-category="art">
                    <div class="game-thumbnail">
                        <img src="https://via.placeholder.com/300x200/219EBC/FFFFFF?text=Digital+Coloring" alt="Digital Coloring Game">
                        <div class="game-overlay">
                            <button class="btn btn-play">
                                <i class="fas fa-play"></i> Play Now
                            </button>
                        </div>
                    </div>
                    <div class="game-info">
                        <div class="game-header">
                            <h3 class="game-title">Digital Coloring</h3>
                            <button class="favorite-btn" title="Add to Favorites">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                        <p class="game-description">Color beautiful pictures with digital crayons!</p>
                        <div class="game-meta">
                            <span class="age-badge">Ages 4-5</span>
                            <span class="category-badge">Art</span>
                        </div>
                    </div>
                </div>

                <div class="game-card" data-game-id="paint-studio" data-age-range="6-7" data-category="art">
                    <div class="game-thumbnail">
                        <img src="https://via.placeholder.com/300x200/FB8500/FFFFFF?text=Paint+Studio" alt="Paint Studio Game">
                        <div class="game-overlay">
                            <button class="btn btn-play">
                                <i class="fas fa-play"></i> Play Now
                            </button>
                        </div>
                    </div>
                    <div class="game-info">
                        <div class="game-header">
                            <h3 class="game-title">Paint Studio</h3>
                            <button class="favorite-btn" title="Add to Favorites">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                        <p class="game-description">Create masterpieces with brushes and paints!</p>
                        <div class="game-meta">
                            <span class="age-badge">Ages 6-7</span>
                            <span class="category-badge">Art</span>
                        </div>
                    </div>
                </div>

                <div class="game-card" data-game-id="art-gallery" data-age-range="8-10" data-category="art">
                    <div class="game-thumbnail">
                        <img src="https://via.placeholder.com/300x200/FFDDD2/023047?text=Art+Gallery" alt="Art Gallery Game">
                        <div class="game-overlay">
                            <button class="btn btn-play">
                                <i class="fas fa-play"></i> Play Now
                            </button>
                        </div>
                    </div>
                    <div class="game-info">
                        <div class="game-header">
                            <h3 class="game-title">Art Gallery</h3>
                            <button class="favorite-btn" title="Add to Favorites">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                        <p class="game-description">Design and curate your own art exhibition!</p>
                        <div class="game-meta">
                            <span class="age-badge">Ages 8-10</span>
                            <span class="category-badge">Art</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3><i class="fas fa-book-open"></i> LSTBook</h3>
                    <p>Making learning fun and accessible for children everywhere.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="games.html">Games</a></li>
                        <li><a href="videos.html">Videos</a></li>
                        <li><a href="explore.html">Explore</a></li>
                        <li><a href="about.html">About Us</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Categories</h4>
                    <ul>
                        <li><a href="games.html?category=animals">Animals</a></li>
                        <li><a href="games.html?category=math">Math</a></li>
                        <li><a href="games.html?category=letters">Letters</a></li>
                        <li><a href="games.html?category=art">Art</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Support</h4>
                    <ul>
                        <li><a href="contact.html">Contact Us</a></li>
                        <li><a href="#">Privacy Policy</a></li>
                        <li><a href="#">Terms of Service</a></li>
                        <li><a href="#">Help Center</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 LSTBook.com - All rights reserved. Made with <i class="fas fa-heart"></i> for kids!</p>
            </div>
        </div>
    </footer>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore-compat.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="js/firebase-config.js"></script>
    <script src="js/main.js"></script>
    <script>
        // Filter Tag Functionality
        document.addEventListener('DOMContentLoaded', function() {
            const filterTags = document.querySelectorAll('.filter-tag');

            filterTags.forEach(tag => {
                tag.addEventListener('click', function() {
                    const filterType = this.dataset.filter;
                    const filterValue = this.dataset.value;

                    // Remove active class from all tags of the same filter type
                    const sameTypeFilters = document.querySelectorAll(`[data-filter="${filterType}"]`);
                    sameTypeFilters.forEach(otherTag => {
                        otherTag.classList.remove('active');
                    });

                    // Add active class to clicked tag
                    this.classList.add('active');

                    // Update game filters
                    updateGameFilters();
                });
            });
        });

        // Filter games function
        function updateGameFilters() {
            const gameCards = document.querySelectorAll('.game-card');

            // Get current filter values
            const activeAgeFilter = document.querySelector('[data-filter="age"].active').dataset.value;
            const activeDifficultyFilter = document.querySelector('[data-filter="difficulty"].active').dataset.value;
            const activeCategoryFilter = document.querySelector('[data-filter="category"].active').dataset.value;

            gameCards.forEach(card => {
                let shouldShow = true;

                // Check age filter
                if (activeAgeFilter !== 'all') {
                    const cardAge = card.dataset.ageRange;
                    if (cardAge !== activeAgeFilter) {
                        shouldShow = false;
                    }
                }

                // Check difficulty filter
                if (activeDifficultyFilter !== 'all') {
                    const cardDifficulty = card.dataset.difficulty;
                    if (cardDifficulty !== activeDifficultyFilter) {
                        shouldShow = false;
                    }
                }

                // Check category filter
                if (activeCategoryFilter !== 'all') {
                    const cardCategory = card.dataset.category;
                    if (cardCategory !== activeCategoryFilter) {
                        shouldShow = false;
                    }
                }

                // Show/hide card with animation
                if (shouldShow) {
                    card.style.display = 'block';
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 10);
                } else {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    setTimeout(() => {
                        card.style.display = 'none';
                    }, 300);
                }
            });
        }
    </script>
</body>
</html>
