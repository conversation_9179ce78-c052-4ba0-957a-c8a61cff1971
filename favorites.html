<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Favorites - LSTBook</title>
    <meta name="description" content="Your favorite games and videos in one place. Sign in to save and access your favorites anytime!">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Fredoka:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/favorites.css">
</head>
<body>
    <!-- Top Bar -->
    <div class="top-bar">
        <div class="container">
            <div class="top-bar-content">
                <div class="welcome-text">
                    <i class="fas fa-heart"></i>
                    Your Favorite Games and Videos!
                </div>
                <div class="auth-buttons">
                    <button class="btn-login" id="loginBtn">
                        <i class="fas fa-user"></i> Login
                    </button>
                    <button class="btn-signup" id="signupBtn">
                        <i class="fas fa-user-plus"></i> Sign Up
                    </button>
                    <div class="user-menu" id="userMenu" style="display: none;">
                        <span class="user-name" id="userName">Welcome!</span>
                        <button class="btn-logout" id="logoutBtn">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <h1><a href="index.html"><i class="fas fa-book-open"></i> LSTBook</a></h1>
                    <p>Learning • Stories • Together</p>
                </div>
                <nav class="main-nav">
                    <ul class="nav-list">
                        <li><a href="index.html" class="nav-link">
                            <i class="fas fa-home"></i> Home
                        </a></li>
                        <li class="has-mega-menu">
                            <a href="games.html" class="nav-link">
                                <i class="fas fa-gamepad"></i> Games
                                <i class="fas fa-chevron-down"></i>
                            </a>
                            <div class="mega-menu">
                                <div class="mega-menu-content">
                                    <div class="mega-menu-grid">
                                        <a href="games.html?category=animals" class="mega-menu-item">
                                            <i class="fas fa-paw"></i>
                                            <span>Animals</span>
                                        </a>
                                        <a href="games.html?category=math" class="mega-menu-item">
                                            <i class="fas fa-calculator"></i>
                                            <span>Numbers & Math</span>
                                        </a>
                                        <a href="games.html?category=letters" class="mega-menu-item">
                                            <i class="fas fa-font"></i>
                                            <span>Letters & Words</span>
                                        </a>
                                        <a href="games.html?category=shapes" class="mega-menu-item">
                                            <i class="fas fa-shapes"></i>
                                            <span>Shapes & Colors</span>
                                        </a>
                                        <a href="games.html?category=logic" class="mega-menu-item">
                                            <i class="fas fa-puzzle-piece"></i>
                                            <span>Logic & Puzzles</span>
                                        </a>
                                        <a href="games.html?category=memory" class="mega-menu-item">
                                            <i class="fas fa-brain"></i>
                                            <span>Memory Games</span>
                                        </a>
                                        <a href="games.html?category=music" class="mega-menu-item">
                                            <i class="fas fa-music"></i>
                                            <span>Music & Sounds</span>
                                        </a>
                                        <a href="games.html?category=stories" class="mega-menu-item">
                                            <i class="fas fa-book"></i>
                                            <span>Interactive Stories</span>
                                        </a>
                                        <a href="games.html?category=coding" class="mega-menu-item">
                                            <i class="fas fa-code"></i>
                                            <span>Learn to Code</span>
                                        </a>
                                        <a href="games.html?category=art" class="mega-menu-item">
                                            <i class="fas fa-palette"></i>
                                            <span>Creativity & Art</span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li><a href="videos.html" class="nav-link">
                            <i class="fas fa-play-circle"></i> Videos
                        </a></li>
                        <li><a href="explore.html" class="nav-link">
                            <i class="fas fa-compass"></i> Explore
                        </a></li>
                        <li><a href="favorites.html" class="nav-link active">
                            <i class="fas fa-heart"></i> Favorites
                        </a></li>
                        <li><a href="about.html" class="nav-link">
                            <i class="fas fa-info-circle"></i> About
                        </a></li>
                        <li><a href="contact.html" class="nav-link">
                            <i class="fas fa-envelope"></i> Contact
                        </a></li>
                    </ul>
                    <div class="mobile-menu-toggle" id="mobileMenuToggle">
                        <i class="fas fa-bars"></i>
                    </div>
                </nav>
            </div>
        </div>
    </header>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <div class="page-header-content">
                <h1 class="page-title">
                    <i class="fas fa-heart"></i> My Favorites
                </h1>
                <p class="page-subtitle">All your favorite games and videos in one special place!</p>
            </div>
        </div>
    </section>

    <!-- Login Required Message (shown when not logged in) -->
    <section class="login-required" id="loginRequired" style="display: none;">
        <div class="container">
            <div class="login-required-content">
                <div class="login-icon">
                    <i class="fas fa-user-lock"></i>
                </div>
                <h2>Login Required</h2>
                <p>You need to be logged in to view and save your favorites!</p>
                <div class="login-buttons">
                    <button class="btn btn-primary" id="loginFromFavorites">
                        <i class="fas fa-sign-in-alt"></i> Login Now
                    </button>
                    <button class="btn btn-secondary" id="signupFromFavorites">
                        <i class="fas fa-user-plus"></i> Create Account
                    </button>
                </div>
                <p class="guest-option">
                    Or <a href="games.html" class="guest-link">browse games as a guest</a>
                </p>
            </div>
        </div>
    </section>

    <!-- Favorites Content -->
    <section class="favorites-section" id="favoritesSection">
        <div class="container">
            <!-- Favorites Stats -->
            <div class="favorites-stats" id="favoritesStats">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-gamepad"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-number" id="gamesCount">0</div>
                        <div class="stat-label">Favorite Games</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-play-circle"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-number" id="videosCount">0</div>
                        <div class="stat-label">Favorite Videos</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-calendar"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-number" id="lastActivity">Today</div>
                        <div class="stat-label">Last Activity</div>
                    </div>
                </div>
            </div>

            <!-- Favorites Filter -->
            <div class="favorites-filter">
                <div class="filter-buttons">
                    <button class="filter-btn active" data-filter="all">
                        <i class="fas fa-th"></i> All Favorites
                    </button>
                    <button class="filter-btn" data-filter="games">
                        <i class="fas fa-gamepad"></i> Games Only
                    </button>
                    <button class="filter-btn" data-filter="videos">
                        <i class="fas fa-play-circle"></i> Videos Only
                    </button>
                </div>
            </div>

            <!-- Favorites Grid -->
            <div class="favorites-grid" id="favoritesContainer">
                <!-- Favorites will be loaded here dynamically -->
                <div class="loading-favorites">
                    <i class="fas fa-spinner fa-spin"></i>
                    <h3>Loading your favorites...</h3>
                    <p>Please wait while we fetch your saved content.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3><i class="fas fa-book-open"></i> LSTBook</h3>
                    <p>Making learning fun and accessible for children everywhere.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="games.html">Games</a></li>
                        <li><a href="videos.html">Videos</a></li>
                        <li><a href="explore.html">Explore</a></li>
                        <li><a href="about.html">About Us</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Categories</h4>
                    <ul>
                        <li><a href="games.html?category=animals">Animals</a></li>
                        <li><a href="games.html?category=math">Math</a></li>
                        <li><a href="games.html?category=letters">Letters</a></li>
                        <li><a href="games.html?category=art">Art</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Support</h4>
                    <ul>
                        <li><a href="contact.html">Contact Us</a></li>
                        <li><a href="#">Privacy Policy</a></li>
                        <li><a href="#">Terms of Service</a></li>
                        <li><a href="#">Help Center</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 LSTBook.com - All rights reserved. Made with <i class="fas fa-heart"></i> for kids!</p>
            </div>
        </div>
    </footer>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore-compat.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="js/firebase-config.js"></script>
    <script src="js/main.js"></script>
    <script src="js/favorites.js"></script>
</body>
</html>
