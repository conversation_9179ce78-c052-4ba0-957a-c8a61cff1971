<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Educational Videos - LSTBook</title>
    <meta name="description" content="Watch fun educational videos for kids aged 4-10. Learn through songs, stories, and interactive content!">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Fredoka:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/videos.css">
</head>
<body>
    <!-- Top Bar -->
    <div class="top-bar">
        <div class="container">
            <div class="top-bar-content">
                <div class="welcome-text">
                    <i class="fas fa-play-circle"></i>
                    Time for Fun Videos! Learn and Laugh Together!
                </div>
                <div class="auth-buttons">
                    <button class="btn-login" id="loginBtn">
                        <i class="fas fa-user"></i> Login
                    </button>
                    <button class="btn-signup" id="signupBtn">
                        <i class="fas fa-user-plus"></i> Sign Up
                    </button>
                    <div class="user-menu" id="userMenu" style="display: none;">
                        <span class="user-name" id="userName">Welcome!</span>
                        <button class="btn-logout" id="logoutBtn">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <h1><a href="index.html"><i class="fas fa-book-open"></i> LSTBook</a></h1>
                    <p>Learning • Stories • Together</p>
                </div>
                <nav class="main-nav">
                    <ul class="nav-list">
                        <li><a href="index.html" class="nav-link">
                            <i class="fas fa-home"></i> Home
                        </a></li>
                        <li class="has-mega-menu">
                            <a href="games.html" class="nav-link">
                                <i class="fas fa-gamepad"></i> Games
                                <i class="fas fa-chevron-down"></i>
                            </a>
                            <div class="mega-menu">
                                <div class="mega-menu-content">
                                    <div class="mega-menu-grid">
                                        <a href="games.html?category=animals" class="mega-menu-item">
                                            <i class="fas fa-paw"></i>
                                            <span>Animals</span>
                                        </a>
                                        <a href="games.html?category=math" class="mega-menu-item">
                                            <i class="fas fa-calculator"></i>
                                            <span>Numbers & Math</span>
                                        </a>
                                        <a href="games.html?category=letters" class="mega-menu-item">
                                            <i class="fas fa-font"></i>
                                            <span>Letters & Words</span>
                                        </a>
                                        <a href="games.html?category=shapes" class="mega-menu-item">
                                            <i class="fas fa-shapes"></i>
                                            <span>Shapes & Colors</span>
                                        </a>
                                        <a href="games.html?category=logic" class="mega-menu-item">
                                            <i class="fas fa-puzzle-piece"></i>
                                            <span>Logic & Puzzles</span>
                                        </a>
                                        <a href="games.html?category=memory" class="mega-menu-item">
                                            <i class="fas fa-brain"></i>
                                            <span>Memory Games</span>
                                        </a>
                                        <a href="games.html?category=music" class="mega-menu-item">
                                            <i class="fas fa-music"></i>
                                            <span>Music & Sounds</span>
                                        </a>
                                        <a href="games.html?category=stories" class="mega-menu-item">
                                            <i class="fas fa-book"></i>
                                            <span>Interactive Stories</span>
                                        </a>
                                        <a href="games.html?category=coding" class="mega-menu-item">
                                            <i class="fas fa-code"></i>
                                            <span>Learn to Code</span>
                                        </a>
                                        <a href="games.html?category=art" class="mega-menu-item">
                                            <i class="fas fa-palette"></i>
                                            <span>Creativity & Art</span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li><a href="videos.html" class="nav-link active">
                            <i class="fas fa-play-circle"></i> Videos
                        </a></li>
                        <li><a href="explore.html" class="nav-link">
                            <i class="fas fa-compass"></i> Explore
                        </a></li>
                        <li><a href="favorites.html" class="nav-link">
                            <i class="fas fa-heart"></i> Favorites
                        </a></li>
                        <li><a href="about.html" class="nav-link">
                            <i class="fas fa-info-circle"></i> About
                        </a></li>
                        <li><a href="contact.html" class="nav-link">
                            <i class="fas fa-envelope"></i> Contact
                        </a></li>
                    </ul>
                    <div class="mobile-menu-toggle" id="mobileMenuToggle">
                        <i class="fas fa-bars"></i>
                    </div>
                </nav>
            </div>
        </div>
    </header>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <div class="page-header-content">
                <h1 class="page-title">
                    <i class="fas fa-play-circle"></i> Educational Videos
                </h1>
                <p class="page-subtitle">Watch fun and educational videos that make learning exciting!</p>
            </div>
        </div>
    </section>

    <!-- Video Filters -->
    <section class="filters-section">
        <div class="container">
            <div class="filters-content">
                <div class="filter-group">
                    <label for="topicFilter">
                        <i class="fas fa-tags"></i> Filter by Topic:
                    </label>
                    <select id="topicFilter" class="filter-select">
                        <option value="all">All Topics</option>
                        <option value="songs">Songs & Music</option>
                        <option value="learning">Learning & Education</option>
                        <option value="stories">Stories & Tales</option>
                    </select>
                </div>
            </div>
        </div>
    </section>

    <!-- Videos Grid -->
    <section class="videos-section">
        <div class="container">
            <div class="videos-grid" id="videosGrid">
                <!-- Songs & Music Videos -->
                <div class="video-card" data-video-id="dQw4w9WgXcQ" data-topic="songs">
                    <div class="video-thumbnail">
                        <iframe 
                            src="https://www.youtube.com/embed/dQw4w9WgXcQ" 
                            title="ABC Song for Kids" 
                            frameborder="0" 
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                            allowfullscreen>
                        </iframe>
                    </div>
                    <div class="video-info">
                        <h3 class="video-title">ABC Song for Kids</h3>
                        <p class="video-description">Learn the alphabet with this catchy and fun ABC song!</p>
                        <div class="video-meta">
                            <span class="duration-badge">3:45</span>
                            <span class="topic-badge">Songs</span>
                        </div>
                    </div>
                </div>

                <div class="video-card" data-video-id="dQw4w9WgXcQ" data-topic="songs">
                    <div class="video-thumbnail">
                        <iframe 
                            src="https://www.youtube.com/embed/dQw4w9WgXcQ" 
                            title="Counting Song 1-10" 
                            frameborder="0" 
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                            allowfullscreen>
                        </iframe>
                    </div>
                    <div class="video-info">
                        <h3 class="video-title">Counting Song 1-10</h3>
                        <p class="video-description">Count from 1 to 10 with this fun musical adventure!</p>
                        <div class="video-meta">
                            <span class="duration-badge">4:12</span>
                            <span class="topic-badge">Songs</span>
                        </div>
                    </div>
                </div>

                <div class="video-card" data-video-id="dQw4w9WgXcQ" data-topic="songs">
                    <div class="video-thumbnail">
                        <iframe 
                            src="https://www.youtube.com/embed/dQw4w9WgXcQ" 
                            title="Colors Song for Children" 
                            frameborder="0" 
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                            allowfullscreen>
                        </iframe>
                    </div>
                    <div class="video-info">
                        <h3 class="video-title">Colors Song for Children</h3>
                        <p class="video-description">Discover all the beautiful colors with this vibrant song!</p>
                        <div class="video-meta">
                            <span class="duration-badge">2:58</span>
                            <span class="topic-badge">Songs</span>
                        </div>
                    </div>
                </div>

                <!-- Learning & Education Videos -->
                <div class="video-card" data-video-id="dQw4w9WgXcQ" data-topic="learning">
                    <div class="video-thumbnail">
                        <iframe 
                            src="https://www.youtube.com/embed/dQw4w9WgXcQ" 
                            title="How Animals Move" 
                            frameborder="0" 
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                            allowfullscreen>
                        </iframe>
                    </div>
                    <div class="video-info">
                        <h3 class="video-title">How Animals Move</h3>
                        <p class="video-description">Learn about different ways animals move and travel!</p>
                        <div class="video-meta">
                            <span class="duration-badge">5:23</span>
                            <span class="topic-badge">Learning</span>
                        </div>
                    </div>
                </div>

                <div class="video-card" data-video-id="dQw4w9WgXcQ" data-topic="learning">
                    <div class="video-thumbnail">
                        <iframe 
                            src="https://www.youtube.com/embed/dQw4w9WgXcQ" 
                            title="Simple Addition for Kids" 
                            frameborder="0" 
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                            allowfullscreen>
                        </iframe>
                    </div>
                    <div class="video-info">
                        <h3 class="video-title">Simple Addition for Kids</h3>
                        <p class="video-description">Make math fun with easy addition problems and examples!</p>
                        <div class="video-meta">
                            <span class="duration-badge">6:15</span>
                            <span class="topic-badge">Learning</span>
                        </div>
                    </div>
                </div>

                <div class="video-card" data-video-id="dQw4w9WgXcQ" data-topic="learning">
                    <div class="video-thumbnail">
                        <iframe 
                            src="https://www.youtube.com/embed/dQw4w9WgXcQ" 
                            title="Weather and Seasons" 
                            frameborder="0" 
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                            allowfullscreen>
                        </iframe>
                    </div>
                    <div class="video-info">
                        <h3 class="video-title">Weather and Seasons</h3>
                        <p class="video-description">Explore different types of weather and the four seasons!</p>
                        <div class="video-meta">
                            <span class="duration-badge">4:47</span>
                            <span class="topic-badge">Learning</span>
                        </div>
                    </div>
                </div>

                <!-- Stories & Tales Videos -->
                <div class="video-card" data-video-id="dQw4w9WgXcQ" data-topic="stories">
                    <div class="video-thumbnail">
                        <iframe 
                            src="https://www.youtube.com/embed/dQw4w9WgXcQ" 
                            title="The Three Little Pigs" 
                            frameborder="0" 
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                            allowfullscreen>
                        </iframe>
                    </div>
                    <div class="video-info">
                        <h3 class="video-title">The Three Little Pigs</h3>
                        <p class="video-description">Enjoy this classic fairy tale with beautiful animations!</p>
                        <div class="video-meta">
                            <span class="duration-badge">8:32</span>
                            <span class="topic-badge">Stories</span>
                        </div>
                    </div>
                </div>

                <div class="video-card" data-video-id="dQw4w9WgXcQ" data-topic="stories">
                    <div class="video-thumbnail">
                        <iframe 
                            src="https://www.youtube.com/embed/dQw4w9WgXcQ" 
                            title="Goldilocks and the Three Bears" 
                            frameborder="0" 
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                            allowfullscreen>
                        </iframe>
                    </div>
                    <div class="video-info">
                        <h3 class="video-title">Goldilocks and the Three Bears</h3>
                        <p class="video-description">Follow Goldilocks on her adventure in the bears' house!</p>
                        <div class="video-meta">
                            <span class="duration-badge">7:18</span>
                            <span class="topic-badge">Stories</span>
                        </div>
                    </div>
                </div>

                <div class="video-card" data-video-id="dQw4w9WgXcQ" data-topic="stories">
                    <div class="video-thumbnail">
                        <iframe 
                            src="https://www.youtube.com/embed/dQw4w9WgXcQ" 
                            title="The Tortoise and the Hare" 
                            frameborder="0" 
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                            allowfullscreen>
                        </iframe>
                    </div>
                    <div class="video-info">
                        <h3 class="video-title">The Tortoise and the Hare</h3>
                        <p class="video-description">Learn about patience and perseverance in this timeless fable!</p>
                        <div class="video-meta">
                            <span class="duration-badge">6:45</span>
                            <span class="topic-badge">Stories</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3><i class="fas fa-book-open"></i> LSTBook</h3>
                    <p>Making learning fun and accessible for children everywhere.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="games.html">Games</a></li>
                        <li><a href="videos.html">Videos</a></li>
                        <li><a href="explore.html">Explore</a></li>
                        <li><a href="about.html">About Us</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Categories</h4>
                    <ul>
                        <li><a href="games.html?category=animals">Animals</a></li>
                        <li><a href="games.html?category=math">Math</a></li>
                        <li><a href="games.html?category=letters">Letters</a></li>
                        <li><a href="games.html?category=art">Art</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Support</h4>
                    <ul>
                        <li><a href="contact.html">Contact Us</a></li>
                        <li><a href="#">Privacy Policy</a></li>
                        <li><a href="#">Terms of Service</a></li>
                        <li><a href="#">Help Center</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 LSTBook.com - All rights reserved. Made with <i class="fas fa-heart"></i> for kids!</p>
            </div>
        </div>
    </footer>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore-compat.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="js/firebase-config.js"></script>
    <script src="js/main.js"></script>
    <script src="js/videos.js"></script>
</body>
</html>
