/* Favorites Page Specific Styles */

/* Page Header */
.page-header {
    background: linear-gradient(135deg, #FB8500 0%, #FFB703 100%);
    color: white;
    padding: 60px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M50,15 L60,35 L80,35 L65,50 L70,70 L50,60 L30,70 L35,50 L20,35 L40,35 Z" fill="rgba(255,255,255,0.1)"/></svg>');
    animation: float 25s infinite linear;
}

.page-header-content {
    position: relative;
    z-index: 2;
}

.page-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 15px;
}

.page-title i {
    color: #FFDDD2;
    margin-right: 15px;
    animation: heartbeat 2s infinite;
}

@keyframes heartbeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.page-subtitle {
    font-size: 1.3rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* Login Required Section */
.login-required {
    padding: 80px 0;
    background: linear-gradient(135deg, #FFDDD2 0%, #8ECAE6 100%);
    text-align: center;
}

.login-required-content {
    max-width: 500px;
    margin: 0 auto;
}

.login-icon {
    width: 100px;
    height: 100px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.login-icon i {
    font-size: 3rem;
    color: #FB8500;
}

.login-required h2 {
    font-size: 2.5rem;
    color: #023047;
    margin-bottom: 20px;
    font-weight: 700;
}

.login-required p {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 30px;
}

.login-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.guest-option {
    font-size: 1rem;
    color: #666;
}

.guest-link {
    color: #219EBC;
    text-decoration: none;
    font-weight: 600;
}

.guest-link:hover {
    color: #023047;
    text-decoration: underline;
}

/* Favorites Section */
.favorites-section {
    padding: 50px 0;
    background: linear-gradient(135deg, #FFDDD2 0%, #8ECAE6 100%);
    min-height: 70vh;
}

/* Favorites Stats */
.favorites-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    gap: 20px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #FFB703, #FB8500);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.stat-icon i {
    font-size: 1.5rem;
    color: white;
}

.stat-info {
    flex: 1;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #023047;
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
    margin-top: 5px;
}

/* Favorites Filter */
.favorites-filter {
    margin-bottom: 30px;
    text-align: center;
}

.filter-buttons {
    display: inline-flex;
    background: white;
    border-radius: 25px;
    padding: 5px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    gap: 5px;
}

.filter-btn {
    padding: 12px 20px;
    border: none;
    background: transparent;
    color: #666;
    border-radius: 20px;
    cursor: pointer;
    font-family: inherit;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-btn:hover {
    color: #023047;
    background: rgba(251, 133, 0, 0.1);
}

.filter-btn.active {
    background: linear-gradient(135deg, #FFB703, #FB8500);
    color: white;
}

.filter-btn i {
    font-size: 1rem;
}

/* Favorites Grid */
.favorites-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
}

/* Loading State */
.loading-favorites {
    grid-column: 1 / -1;
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.loading-favorites i {
    font-size: 4rem;
    color: #8ECAE6;
    margin-bottom: 20px;
    display: block;
}

.loading-favorites h3 {
    font-size: 1.8rem;
    color: #023047;
    margin-bottom: 15px;
    font-weight: 600;
}

.loading-favorites p {
    font-size: 1.1rem;
    max-width: 400px;
    margin: 0 auto;
}

/* No Favorites State */
.no-favorites {
    grid-column: 1 / -1;
    text-align: center;
    padding: 80px 20px;
    color: #666;
}

.no-favorites i {
    font-size: 5rem;
    color: #FFB703;
    margin-bottom: 30px;
    display: block;
}

.no-favorites h3 {
    font-size: 2.2rem;
    color: #023047;
    margin-bottom: 20px;
    font-weight: 700;
}

.no-favorites p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

/* Favorite Item Cards */
.favorite-item {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.favorite-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.2);
}

.favorite-item.game-item {
    border-top: 4px solid #8ECAE6;
}

.favorite-item.video-item {
    border-top: 4px solid #219EBC;
}

.favorite-thumbnail {
    position: relative;
    height: 180px;
    overflow: hidden;
}

.favorite-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.favorite-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(2, 48, 71, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.favorite-item:hover .favorite-overlay {
    opacity: 1;
}

.play-btn {
    background: #FFB703;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.play-btn:hover {
    background: #FB8500;
    transform: scale(1.05);
}

.favorite-info {
    padding: 20px;
}

.favorite-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.favorite-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #023047;
    margin: 0;
    flex: 1;
    line-height: 1.3;
}

.remove-favorite {
    background: none;
    border: none;
    color: #FB8500;
    font-size: 1.3rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
    margin-left: 10px;
}

.remove-favorite:hover {
    background: rgba(251, 133, 0, 0.1);
    transform: scale(1.1);
}

.favorite-description {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 15px;
}

.favorite-meta {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.favorite-badge {
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.type-badge {
    background: linear-gradient(135deg, #8ECAE6, #219EBC);
    color: white;
}

.category-badge {
    background: linear-gradient(135deg, #FFDDD2, #FFB703);
    color: #023047;
}

.age-badge {
    background: linear-gradient(135deg, #FFB703, #FB8500);
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-title {
        font-size: 2.2rem;
    }
    
    .page-subtitle {
        font-size: 1.1rem;
    }
    
    .login-required h2 {
        font-size: 2rem;
    }
    
    .login-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .favorites-stats {
        grid-template-columns: 1fr;
    }
    
    .filter-buttons {
        flex-direction: column;
        width: 100%;
        max-width: 300px;
        margin: 0 auto;
    }
    
    .filter-btn {
        justify-content: center;
        width: 100%;
    }
    
    .favorites-grid {
        grid-template-columns: 1fr;
    }
    
    .favorite-item {
        max-width: 400px;
        margin: 0 auto;
    }
}

@media (max-width: 480px) {
    .page-header {
        padding: 40px 0;
    }
    
    .page-title {
        font-size: 1.8rem;
    }
    
    .favorites-section {
        padding: 30px 0;
    }
    
    .stat-card {
        padding: 20px;
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .favorite-info {
        padding: 15px;
    }
}

/* Animation for favorite items appearing */
.favorite-item {
    animation: fadeInUp 0.6s ease forwards;
    opacity: 0;
    transform: translateY(30px);
}

.favorite-item:nth-child(1) { animation-delay: 0.1s; }
.favorite-item:nth-child(2) { animation-delay: 0.2s; }
.favorite-item:nth-child(3) { animation-delay: 0.3s; }
.favorite-item:nth-child(4) { animation-delay: 0.4s; }
.favorite-item:nth-child(5) { animation-delay: 0.5s; }
.favorite-item:nth-child(6) { animation-delay: 0.6s; }

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Logo link styling */
.logo h1 a {
    color: inherit;
    text-decoration: none;
}

.logo h1 a:hover {
    color: #8ECAE6;
    transition: color 0.3s ease;
}
