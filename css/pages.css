/* Static Pages CSS - About, Explore, Contact */

/* Page Headers */
.page-header {
    padding: 60px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
    color: white;
}

.about-header {
    background: linear-gradient(135deg, #FB8500 0%, #FFB703 100%);
}

.explore-header {
    background: linear-gradient(135deg, #219EBC 0%, #8ECAE6 100%);
}

.contact-header {
    background: linear-gradient(135deg, #8ECAE6 0%, #219EBC 100%);
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.1;
    animation: float 20s infinite linear;
}

.page-header-content {
    position: relative;
    z-index: 2;
}

.page-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 15px;
}

.page-title i {
    color: #FFDDD2;
    margin-right: 15px;
}

.page-subtitle {
    font-size: 1.3rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* Section Titles */
.section-title {
    text-align: center;
    font-size: 2.5rem;
    color: #023047;
    margin-bottom: 50px;
    font-weight: 700;
}

.section-title i {
    color: #FB8500;
    margin-right: 15px;
}

/* About Page Styles */
.mission-section {
    padding: 80px 0;
    background: white;
}

.mission-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.mission-text h2 {
    font-size: 2.2rem;
    color: #023047;
    margin-bottom: 25px;
    font-weight: 700;
}

.mission-text h2 i {
    color: #FB8500;
    margin-right: 15px;
}

.mission-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #666;
    margin-bottom: 20px;
}

.mission-illustration {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    height: 300px;
}

.mission-illustration i {
    position: absolute;
    font-size: 4rem;
    animation: bounce 3s infinite;
}

.mission-illustration i:nth-child(1) {
    top: 20%;
    left: 20%;
    color: #FFB703;
    animation-delay: 0s;
}

.mission-illustration i:nth-child(2) {
    top: 10%;
    right: 30%;
    color: #219EBC;
    animation-delay: 0.5s;
}

.mission-illustration i:nth-child(3) {
    bottom: 30%;
    left: 30%;
    color: #FB8500;
    animation-delay: 1s;
}

.mission-illustration i:nth-child(4) {
    bottom: 20%;
    right: 20%;
    color: #8ECAE6;
    animation-delay: 1.5s;
}

.values-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #FFDDD2 0%, #8ECAE6 100%);
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.value-card {
    background: white;
    padding: 40px 30px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.value-card:hover {
    transform: translateY(-10px);
}

.value-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #FFB703, #FB8500);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.value-icon i {
    font-size: 2.5rem;
    color: white;
}

.value-card h3 {
    font-size: 1.5rem;
    color: #023047;
    margin-bottom: 15px;
    font-weight: 600;
}

.value-card p {
    color: #666;
    line-height: 1.6;
}

.features-section {
    padding: 80px 0;
    background: white;
}

.features-grid {
    display: grid;
    gap: 30px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 30px;
    padding: 30px;
    background: linear-gradient(135deg, #FFDDD2 0%, rgba(142, 202, 230, 0.3) 100%);
    border-radius: 20px;
    transition: transform 0.3s ease;
}

.feature-item:hover {
    transform: translateX(10px);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #8ECAE6, #219EBC);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.feature-icon i {
    font-size: 2rem;
    color: white;
}

.feature-content h3 {
    font-size: 1.4rem;
    color: #023047;
    margin-bottom: 10px;
    font-weight: 600;
}

.feature-content p {
    color: #666;
    line-height: 1.6;
}

.team-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #8ECAE6 0%, #219EBC 100%);
    color: white;
}

.team-section .section-title {
    color: white;
}

.team-section .section-title i {
    color: #FFDDD2;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
}

.team-member {
    text-align: center;
    padding: 30px;
}

.member-avatar {
    width: 100px;
    height: 100px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.member-avatar i {
    font-size: 3rem;
    color: white;
}

.team-member h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    font-weight: 600;
}

.team-member p {
    line-height: 1.6;
    opacity: 0.9;
}

/* Explore Page Styles */
.fun-facts-section {
    padding: 80px 0;
    background: white;
}

.facts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
}

.fact-card {
    background: linear-gradient(135deg, #FFDDD2 0%, #8ECAE6 100%);
    padding: 30px;
    border-radius: 20px;
    text-align: center;
    transition: transform 0.3s ease;
}

.fact-card:hover {
    transform: translateY(-5px);
}

.fact-icon {
    width: 70px;
    height: 70px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.fact-icon i {
    font-size: 2rem;
    color: #FB8500;
}

.fact-card h3 {
    font-size: 1.3rem;
    color: #023047;
    margin-bottom: 15px;
    font-weight: 600;
}

.fact-card p {
    color: #666;
    line-height: 1.6;
}

.quiz-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #8ECAE6 0%, #219EBC 100%);
}

.quiz-section .section-title {
    color: white;
}

.quiz-section .section-title i {
    color: #FFDDD2;
}

.quiz-container {
    max-width: 600px;
    margin: 0 auto;
}

.quiz-card {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.quiz-question h3 {
    font-size: 1.5rem;
    color: #023047;
    margin-bottom: 30px;
    text-align: center;
    font-weight: 600;
}

.quiz-options {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-bottom: 30px;
}

.quiz-option {
    padding: 15px 20px;
    border: 2px solid #8ECAE6;
    background: white;
    border-radius: 15px;
    cursor: pointer;
    font-family: inherit;
    font-weight: 500;
    transition: all 0.3s ease;
}

.quiz-option:hover {
    background: #8ECAE6;
    color: white;
    transform: translateY(-2px);
}

.quiz-result {
    text-align: center;
}

.result-icon i {
    font-size: 4rem;
    color: #28a745;
    margin-bottom: 20px;
}

.result-text {
    font-size: 1.2rem;
    color: #023047;
    margin-bottom: 20px;
    font-weight: 600;
}

.activities-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #FFDDD2 0%, #8ECAE6 100%);
}

.activities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
}

.activity-card {
    background: white;
    padding: 30px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.activity-card:hover {
    transform: translateY(-10px);
}

.activity-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #FFB703, #FB8500);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.activity-icon i {
    font-size: 2rem;
    color: white;
}

.activity-card h3 {
    font-size: 1.3rem;
    color: #023047;
    margin-bottom: 15px;
    font-weight: 600;
}

.activity-card p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 20px;
}

.tips-section {
    padding: 80px 0;
    background: white;
}

.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.tip-card {
    background: linear-gradient(135deg, #FFDDD2 0%, rgba(142, 202, 230, 0.3) 100%);
    padding: 30px;
    border-radius: 20px;
    text-align: center;
    position: relative;
    transition: transform 0.3s ease;
}

.tip-card:hover {
    transform: translateY(-5px);
}

.tip-number {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #FFB703, #FB8500);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.2rem;
}

.tip-card h3 {
    font-size: 1.3rem;
    color: #023047;
    margin: 20px 0 15px;
    font-weight: 600;
}

.tip-card p {
    color: #666;
    line-height: 1.6;
}

/* Contact Page Styles */
.contact-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #FFDDD2 0%, #8ECAE6 100%);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
}

.contact-form-container, .contact-info-container {
    background: white;
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.contact-form-container h2, .contact-info-container h2 {
    font-size: 1.8rem;
    color: #023047;
    margin-bottom: 30px;
    font-weight: 600;
}

.contact-form-container h2 i, .contact-info-container h2 i {
    color: #FB8500;
    margin-right: 10px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #023047;
    font-weight: 600;
}

.form-group label i {
    color: #8ECAE6;
    margin-right: 8px;
}

.form-group input, .form-group select, .form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #8ECAE6;
    border-radius: 10px;
    font-family: inherit;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus, .form-group select:focus, .form-group textarea:focus {
    outline: none;
    border-color: #219EBC;
}

.submit-btn {
    width: 100%;
    padding: 15px;
    font-size: 1.1rem;
}

.contact-info {
    margin-bottom: 40px;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    margin-bottom: 30px;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #8ECAE6, #219EBC);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.contact-icon i {
    font-size: 1.3rem;
    color: white;
}

.contact-details h3 {
    font-size: 1.2rem;
    color: #023047;
    margin-bottom: 8px;
    font-weight: 600;
}

.contact-details p {
    color: #666;
    margin-bottom: 5px;
}

.faq-section h3 {
    font-size: 1.4rem;
    color: #023047;
    margin-bottom: 20px;
    font-weight: 600;
}

.faq-section h3 i {
    color: #FB8500;
    margin-right: 10px;
}

.faq-item {
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.faq-item:last-child {
    border-bottom: none;
}

.faq-item h4 {
    font-size: 1.1rem;
    color: #023047;
    margin-bottom: 8px;
    font-weight: 600;
}

.faq-item p {
    color: #666;
    line-height: 1.6;
}

/* CTA Section */
.cta-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #023047 0%, #219EBC 100%);
    color: white;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    font-weight: 700;
}

.cta-content p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-title {
        font-size: 2.2rem;
    }
    
    .mission-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }
    
    .values-grid {
        grid-template-columns: 1fr;
    }
    
    .feature-item {
        flex-direction: column;
        text-align: center;
    }
    
    .team-grid {
        grid-template-columns: 1fr;
    }
    
    .quiz-options {
        grid-template-columns: 1fr;
    }
    
    .contact-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
}

@media (max-width: 480px) {
    .page-header {
        padding: 40px 0;
    }
    
    .page-title {
        font-size: 1.8rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .contact-form-container, .contact-info-container {
        padding: 25px;
    }
}
