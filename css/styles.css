/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Fredoka', sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #FFDDD2 0%, #8ECAE6 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Top Bar */
.top-bar {
    background: #FFB703;
    color: white;
    padding: 8px 0;
    font-size: 14px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.top-bar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.welcome-text {
    font-weight: 500;
}

.welcome-text i {
    color: #FB8500;
    margin-right: 5px;
    animation: twinkle 2s infinite;
}

@keyframes twinkle {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.auth-buttons {
    display: flex;
    gap: 10px;
    align-items: center;
}

.btn-login, .btn-signup, .btn-logout {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    padding: 5px 12px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
    font-family: inherit;
}

.btn-login:hover, .btn-signup:hover, .btn-logout:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-1px);
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 10px;
}

.user-name {
    font-weight: 600;
    color: #023047;
}

/* Header */
.header {
    background: #1e5c7a;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
}

.logo h1 {
    color: white;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.logo h1 i {
    color: #FFB703;
    margin-right: 10px;
}

.logo p {
    color: #8ECAE6;
    font-size: 0.9rem;
    font-weight: 500;
    letter-spacing: 1px;
}

/* Navigation */
.main-nav {
    position: relative;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: 5px;
    align-items: center;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    text-decoration: none;
    color: #8ECAE6;
    font-weight: 500;
    border-radius: 25px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover, .nav-link.active {
    background: linear-gradient(135deg, #FFB703, #FB8500);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 183, 3, 0.4);
}

.nav-link i {
    font-size: 1.1rem;
}

/* Mega Menu */
.has-mega-menu {
    position: relative;
}

.mega-menu {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #FFDDD2 0%, #8ECAE6 100%);
    border-radius: 25px;
    box-shadow: 0 15px 40px rgba(0,0,0,0.2);
    opacity: 0;
    visibility: hidden;
    transition: all 0.4s ease;
    z-index: 1000;
    margin-top: 15px;
    min-width: 700px;
    border: 4px solid white;
}

.has-mega-menu:hover .mega-menu {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(0);
}

.mega-menu-content {
    padding: 40px;
}

.mega-menu-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 25px;
}

.mega-menu-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 25px 15px;
    text-decoration: none;
    color: #023047;
    background: white;
    border-radius: 20px;
    transition: all 0.4s ease;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}

.mega-menu-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #FFB703, #FB8500);
    opacity: 0;
    transition: opacity 0.4s ease;
    border-radius: 20px;
}

.mega-menu-item:hover::before {
    opacity: 1;
}

.mega-menu-item:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow: 0 15px 30px rgba(255, 183, 3, 0.3);
    color: white;
}

.mega-menu-item i {
    font-size: 3rem;
    margin-bottom: 12px;
    transition: all 0.4s ease;
    position: relative;
    z-index: 2;
}

.mega-menu-item:nth-child(1) i { color: #FF6B6B; }
.mega-menu-item:nth-child(2) i { color: #4ECDC4; }
.mega-menu-item:nth-child(3) i { color: #45B7D1; }
.mega-menu-item:nth-child(4) i { color: #96CEB4; }
.mega-menu-item:nth-child(5) i { color: #FFEAA7; }
.mega-menu-item:nth-child(6) i { color: #DDA0DD; }
.mega-menu-item:nth-child(7) i { color: #98D8C8; }
.mega-menu-item:nth-child(8) i { color: #F7DC6F; }
.mega-menu-item:nth-child(9) i { color: #BB8FCE; }
.mega-menu-item:nth-child(10) i { color: #85C1E9; }

.mega-menu-item:hover i {
    color: white;
    transform: scale(1.2) rotate(5deg);
}

.mega-menu-item span {
    font-size: 1rem;
    font-weight: 600;
    position: relative;
    z-index: 2;
    transition: all 0.4s ease;
}

.mega-menu-item:hover span {
    transform: translateY(-2px);
}

/* Fun animations for mega menu items */
.mega-menu-item:nth-child(1) { animation-delay: 0.1s; }
.mega-menu-item:nth-child(2) { animation-delay: 0.2s; }
.mega-menu-item:nth-child(3) { animation-delay: 0.3s; }
.mega-menu-item:nth-child(4) { animation-delay: 0.4s; }
.mega-menu-item:nth-child(5) { animation-delay: 0.5s; }
.mega-menu-item:nth-child(6) { animation-delay: 0.6s; }
.mega-menu-item:nth-child(7) { animation-delay: 0.7s; }
.mega-menu-item:nth-child(8) { animation-delay: 0.8s; }
.mega-menu-item:nth-child(9) { animation-delay: 0.9s; }
.mega-menu-item:nth-child(10) { animation-delay: 1.0s; }

.has-mega-menu:hover .mega-menu-item {
    animation: bounceIn 0.6s ease forwards;
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3) translateY(20px);
    }
    50% {
        opacity: 1;
        transform: scale(1.05) translateY(-5px);
    }
    70% {
        transform: scale(0.95) translateY(2px);
    }
    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    background: #FFB703;
    color: white;
    padding: 10px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.mobile-menu-toggle:hover {
    background: #FB8500;
    transform: scale(1.05);
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #FFB703 0%, #FB8500 100%);
    color: white;
    padding: 80px 0;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
    animation: float 20s infinite linear;
}

@keyframes float {
    0% { transform: translateY(0px); }
    100% { transform: translateY(-100px); }
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
    align-items: center;
    position: relative;
    z-index: 2;
}

.hero-text h2 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.2;
}

.hero-text p {
    font-size: 1.3rem;
    margin-bottom: 30px;
    opacity: 0.95;
}

.hero-buttons {
    display: flex;
    gap: 20px;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-family: inherit;
}

.btn-primary {
    background: white;
    color: #FB8500;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.btn-secondary {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
}

.btn-secondary:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-3px);
}

.hero-illustration {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    height: 300px;
}

.hero-illustration i {
    position: absolute;
    font-size: 4rem;
    animation: bounce 3s infinite;
}

.hero-illustration i:nth-child(1) {
    top: 20%;
    left: 20%;
    color: #FFDDD2;
    animation-delay: 0s;
}

.hero-illustration i:nth-child(2) {
    top: 10%;
    right: 30%;
    color: #219EBC;
    animation-delay: 0.5s;
}

.hero-illustration i:nth-child(3) {
    bottom: 30%;
    left: 30%;
    color: #FFDDD2;
    animation-delay: 1s;
}

.hero-illustration i:nth-child(4) {
    bottom: 20%;
    right: 20%;
    color: #219EBC;
    animation-delay: 1.5s;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-20px);
    }
    60% {
        transform: translateY(-10px);
    }
}

/* Featured Categories */
.featured-categories {
    padding: 80px 0;
    background: white;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    color: #023047;
    margin-bottom: 50px;
    font-weight: 700;
}

.section-title i {
    color: #FB8500;
    margin-right: 15px;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.category-card {
    background: linear-gradient(135deg, #FFDDD2 0%, #8ECAE6 100%);
    padding: 40px 30px;
    border-radius: 20px;
    text-align: center;
    text-decoration: none;
    color: #023047;
    transition: all 0.3s ease;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.category-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.2);
}

.category-icon {
    width: 80px;
    height: 80px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.category-icon i {
    font-size: 2.5rem;
    color: #FB8500;
}

.category-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.category-card p {
    opacity: 0.8;
    font-size: 1rem;
}

/* Quick Stats */
.quick-stats {
    background: linear-gradient(135deg, #219EBC 0%, #8ECAE6 100%);
    padding: 60px 0;
    color: white;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 40px;
}

.stat-item {
    text-align: center;
}

.stat-icon {
    width: 70px;
    height: 70px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
}

.stat-icon i {
    font-size: 2rem;
    color: white;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Footer */
.footer {
    background: #023047;
    color: white;
    padding: 50px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 30px;
}

.footer-section h3 {
    color: #FFB703;
    font-size: 1.5rem;
    margin-bottom: 20px;
    font-weight: 600;
}

.footer-section h3 i {
    margin-right: 10px;
}

.footer-section h4 {
    color: #8ECAE6;
    font-size: 1.2rem;
    margin-bottom: 15px;
    font-weight: 600;
}

.footer-section p {
    opacity: 0.8;
    line-height: 1.6;
    margin-bottom: 20px;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 8px;
}

.footer-section ul li a {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #FFB703;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: #FFB703;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: #FB8500;
    transform: translateY(-3px);
}

.footer-bottom {
    border-top: 1px solid rgba(255,255,255,0.1);
    padding-top: 20px;
    text-align: center;
    opacity: 0.8;
}

.footer-bottom i {
    color: #FB8500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .top-bar-content {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .header-content {
        flex-direction: column;
        gap: 20px;
    }
    
    .nav-list {
        display: none;
        flex-direction: column;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: #1e5c7a;
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        border-radius: 0 0 15px 15px;
        padding: 20px;
        gap: 10px;
        border-top: 2px solid #8ECAE6;
    }
    
    .nav-list.active {
        display: flex;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    .mega-menu {
        position: static;
        transform: none;
        margin-top: 15px;
        min-width: auto;
        width: 100%;
        border-radius: 20px;
        border: 3px solid white;
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .mega-menu-content {
        padding: 25px;
    }

    .mega-menu-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .mega-menu-item {
        padding: 20px 15px;
        border-radius: 15px;
    }

    .mega-menu-item i {
        font-size: 2.5rem;
        margin-bottom: 10px;
    }

    .mega-menu-item span {
        font-size: 0.9rem;
    }

    .mega-menu-item:hover {
        transform: translateY(-5px) scale(1.02);
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 30px;
    }
    
    .hero-text h2 {
        font-size: 2.5rem;
    }
    
    .hero-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .categories-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .hero-text h2 {
        font-size: 2rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .mega-menu-grid {
        grid-template-columns: 1fr;
    }
}
