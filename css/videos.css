/* Videos Page Specific Styles */

/* Page Header */
.page-header {
    background: linear-gradient(135deg, #219EBC 0%, #8ECAE6 100%);
    color: white;
    padding: 60px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><polygon points="20,15 35,25 20,35" fill="rgba(255,255,255,0.1)"/><polygon points="60,45 75,55 60,65" fill="rgba(255,255,255,0.1)"/><polygon points="40,75 55,85 40,95" fill="rgba(255,255,255,0.1)"/></svg>');
    animation: float 20s infinite linear;
}

.page-header-content {
    position: relative;
    z-index: 2;
}

.page-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 15px;
}

.page-title i {
    color: #FFDDD2;
    margin-right: 15px;
}

.page-subtitle {
    font-size: 1.3rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* Filters Section */
.filters-section {
    background: white;
    padding: 30px 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 80px;
    z-index: 100;
}

.filters-content {
    display: flex;
    gap: 30px;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-group label {
    font-weight: 600;
    color: #023047;
    font-size: 1rem;
}

.filter-group label i {
    color: #219EBC;
    margin-right: 5px;
}

.filter-select {
    padding: 10px 15px;
    border: 2px solid #8ECAE6;
    border-radius: 25px;
    background: white;
    color: #023047;
    font-family: inherit;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 180px;
}

.filter-select:focus {
    outline: none;
    border-color: #219EBC;
    box-shadow: 0 0 0 3px rgba(33, 158, 188, 0.2);
}

.filter-select:hover {
    border-color: #219EBC;
}

/* Videos Section */
.videos-section {
    padding: 50px 0;
    background: linear-gradient(135deg, #FFDDD2 0%, #8ECAE6 100%);
    min-height: 70vh;
}

.videos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
}

/* Video Cards */
.video-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.video-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.2);
}

.video-thumbnail {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.video-thumbnail iframe {
    width: 100%;
    height: 100%;
    border: none;
    transition: transform 0.3s ease;
}

.video-card:hover .video-thumbnail iframe {
    transform: scale(1.05);
}

/* Video Info */
.video-info {
    padding: 20px;
}

.video-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #023047;
    margin: 0 0 10px 0;
    line-height: 1.3;
}

.video-description {
    color: #666;
    font-size: 0.95rem;
    line-height: 1.5;
    margin-bottom: 15px;
}

.video-meta {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    align-items: center;
}

.duration-badge, .topic-badge {
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.duration-badge {
    background: linear-gradient(135deg, #FFB703, #FB8500);
    color: white;
}

.topic-badge {
    background: linear-gradient(135deg, #8ECAE6, #219EBC);
    color: white;
}

/* Play Button Overlay */
.video-thumbnail::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: rgba(255, 183, 3, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.video-card:hover .video-thumbnail::after {
    opacity: 1;
}

.video-thumbnail::before {
    content: '\f04b';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 1.5rem;
    z-index: 2;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.video-card:hover .video-thumbnail::before {
    opacity: 1;
}

/* Loading and Error States */
.loading-message, .error-message, .no-videos {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.loading-message i, .error-message i, .no-videos i {
    font-size: 4rem;
    color: #8ECAE6;
    margin-bottom: 20px;
    display: block;
}

.error-message i {
    color: #FB8500;
}

.no-videos i {
    color: #219EBC;
}

.loading-message h3, .error-message h3, .no-videos h3 {
    font-size: 1.8rem;
    color: #023047;
    margin-bottom: 15px;
    font-weight: 600;
}

.loading-message p, .error-message p, .no-videos p {
    font-size: 1.1rem;
    margin-bottom: 25px;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-title {
        font-size: 2.2rem;
    }
    
    .page-subtitle {
        font-size: 1.1rem;
    }
    
    .filters-content {
        flex-direction: column;
        gap: 20px;
    }
    
    .filter-group {
        flex-direction: column;
        text-align: center;
        gap: 8px;
    }
    
    .filter-select {
        min-width: 200px;
    }
    
    .videos-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .video-card {
        max-width: 500px;
        margin: 0 auto;
    }
    
    .filters-section {
        position: static;
    }
}

@media (max-width: 480px) {
    .page-header {
        padding: 40px 0;
    }
    
    .page-title {
        font-size: 1.8rem;
    }
    
    .page-subtitle {
        font-size: 1rem;
    }
    
    .videos-section {
        padding: 30px 0;
    }
    
    .video-info {
        padding: 15px;
    }
    
    .video-title {
        font-size: 1.1rem;
    }
    
    .videos-grid {
        grid-template-columns: 1fr;
    }
    
    .video-thumbnail {
        height: 180px;
    }
}

/* Animation for video cards appearing */
.video-card {
    animation: fadeInUp 0.6s ease forwards;
    opacity: 0;
    transform: translateY(30px);
}

.video-card:nth-child(1) { animation-delay: 0.1s; }
.video-card:nth-child(2) { animation-delay: 0.2s; }
.video-card:nth-child(3) { animation-delay: 0.3s; }
.video-card:nth-child(4) { animation-delay: 0.4s; }
.video-card:nth-child(5) { animation-delay: 0.5s; }
.video-card:nth-child(6) { animation-delay: 0.6s; }
.video-card:nth-child(7) { animation-delay: 0.7s; }
.video-card:nth-child(8) { animation-delay: 0.8s; }
.video-card:nth-child(9) { animation-delay: 0.9s; }

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Logo link styling */
.logo h1 a {
    color: inherit;
    text-decoration: none;
}

.logo h1 a:hover {
    color: #8ECAE6;
    transition: color 0.3s ease;
}

/* Topic-specific styling */
.video-card[data-topic="songs"] .topic-badge {
    background: linear-gradient(135deg, #FFB703, #FB8500);
}

.video-card[data-topic="learning"] .topic-badge {
    background: linear-gradient(135deg, #8ECAE6, #219EBC);
}

.video-card[data-topic="stories"] .topic-badge {
    background: linear-gradient(135deg, #FFDDD2, #FFB703);
    color: #023047;
}
