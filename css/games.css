/* Games Page Specific Styles */

/* Page Header */
.page-header {
    background: linear-gradient(135deg, #FFB703 0%, #FB8500 100%);
    color: white;
    padding: 60px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="90" cy="30" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="30" cy="90" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="70" cy="70" r="1.2" fill="rgba(255,255,255,0.1)"/></svg>');
    animation: float 15s infinite linear;
}

.page-header-content {
    position: relative;
    z-index: 2;
}

.page-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 15px;
}

.page-title i {
    color: #FFDDD2;
    margin-right: 15px;
}

.page-subtitle {
    font-size: 1.3rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* Filter Tags in Header */
.filter-tags-container {
    margin-top: 30px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    align-items: flex-start;
}

.filter-row {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
    justify-content: flex-start;
    width: 100%;
}

.filter-tags {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: center;
}

.filter-tag {
    background: rgba(255, 255, 255, 0.9);
    color: #2c3e50;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 15px;
    padding: 10px 18px;
    font-weight: 600;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    text-shadow: none;
}

.filter-tag:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.filter-tag.active {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.2);
    font-weight: 700;
}

.filter-tag i {
    font-size: 1rem;
}

/* Special styling for filter label tags */
.filter-label-tag {
    cursor: default !important;
    min-width: 100px;
    justify-content: center;
    font-weight: 800 !important;
    font-size: 1rem !important;
    padding: 12px 20px !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2) !important;
    border-width: 2px !important;
}

.filter-label-tag:hover {
    transform: none !important;
}

.filter-label-tag i {
    font-size: 1.1rem !important;
    margin-right: 6px;
}

/* Colored backgrounds for different label types */
.filter-label-tag[data-filter="age"] {
    background: linear-gradient(135deg, #8ECAE6 0%, #219EBC 100%) !important;
    border-color: rgba(142, 202, 230, 0.8) !important;
    color: white !important;
}

.filter-label-tag[data-filter="difficulty"] {
    background: linear-gradient(135deg, #FF6B6B 0%, #FF1744 100%) !important;
    border-color: rgba(255, 107, 107, 0.8) !important;
    color: white !important;
}

.filter-label-tag[data-filter="category"] {
    background: linear-gradient(135deg, #4ECDC4 0%, #00BCD4 100%) !important;
    border-color: rgba(78, 205, 196, 0.8) !important;
    color: white !important;
}

/* Individual Filter Tag Colors */

/* Age Filter Colors - Blue Tones */
.filter-tag[data-value="4-5"] {
    background: #E3F2FD !important;
    border-color: #BBDEFB !important;
}

.filter-tag[data-value="6-7"] {
    background: #E1F5FE !important;
    border-color: #B3E5FC !important;
}

.filter-tag[data-value="8-10"] {
    background: #E0F2F1 !important;
    border-color: #B2DFDB !important;
}

/* Difficulty Filter Colors - Traffic Light System */
.filter-tag[data-value="easy"] {
    background: #E8F5E8 !important;
    border-color: #C8E6C9 !important;
}

.filter-tag[data-value="medium"] {
    background: #FFF8E1 !important;
    border-color: #FFECB3 !important;
}

.filter-tag[data-value="hard"] {
    background: #FFEBEE !important;
    border-color: #FFCDD2 !important;
}

/* Category Filter Colors - Rainbow System */
.filter-tag[data-value="animals"] {
    background: #FCE4EC !important;
    border-color: #F8BBD9 !important;
}

.filter-tag[data-value="math"] {
    background: #F3E5F5 !important;
    border-color: #E1BEE7 !important;
}

.filter-tag[data-value="letters"] {
    background: #E8EAF6 !important;
    border-color: #C5CAE9 !important;
}

.filter-tag[data-value="shapes"] {
    background: #E3F2FD !important;
    border-color: #BBDEFB !important;
}

.filter-tag[data-value="logic"] {
    background: #E0F2F1 !important;
    border-color: #B2DFDB !important;
}

.filter-tag[data-value="memory"] {
    background: #E0F7FA !important;
    border-color: #B2EBF2 !important;
}

.filter-tag[data-value="music"] {
    background: #FFF3E0 !important;
    border-color: #FFE0B2 !important;
}

.filter-tag[data-value="stories"] {
    background: #FFF8E1 !important;
    border-color: #FFECB3 !important;
}

.filter-tag[data-value="coding"] {
    background: #F1F8E9 !important;
    border-color: #DCEDC8 !important;
}

.filter-tag[data-value="art"] {
    background: #FFEBEE !important;
    border-color: #FFCDD2 !important;
}

/* Games Section */

/* Mobile Responsive Styles for Filter Tags */
@media (max-width: 768px) {
    .filter-tags-container {
        gap: 15px;
        align-items: center;
    }

    .filter-row {
        gap: 6px;
        justify-content: center;
    }

    .filter-tags {
        gap: 6px;
        justify-content: center;
    }

    .filter-tag {
        padding: 8px 14px;
        font-size: 0.85rem;
        border-radius: 15px;
    }

    .filter-label-tag {
        padding: 10px 16px !important;
        font-size: 0.9rem !important;
        min-width: 80px;
    }

    .filter-tag:hover {
        transform: translateY(-1px);
    }
}

/* Games Section */


.games-section {
    padding: 50px 0;
    background: linear-gradient(135deg, #FFDDD2 0%, #8ECAE6 100%);
    min-height: 70vh;
}

.games-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
}

/* Game Cards */
.game-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.game-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.2);
}

.game-thumbnail {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.game-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.game-card:hover .game-thumbnail img {
    transform: scale(1.1);
}

/* Game Placeholder Styles */
.game-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.game-placeholder::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(1px);
}

.game-placeholder i {
    font-size: 4rem;
    margin-bottom: 10px;
    position: relative;
    z-index: 2;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    animation: float 3s ease-in-out infinite;
}

.game-placeholder span {
    font-size: 1.2rem;
    font-weight: 700;
    position: relative;
    z-index: 2;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    letter-spacing: 1px;
}

.game-card:hover .game-placeholder {
    transform: scale(1.1);
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.game-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(2, 48, 71, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.game-card:hover .game-overlay {
    opacity: 1;
}

.btn-play {
    background: #FFB703;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: inherit;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-play:hover {
    background: #FB8500;
    transform: scale(1.05);
}

.btn-play i {
    font-size: 1.1rem;
}

/* Game Info */
.game-info {
    padding: 20px;
}

.game-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.game-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #023047;
    margin: 0;
    flex: 1;
    line-height: 1.3;
}

.favorite-btn {
    background: none;
    border: none;
    color: #ccc;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 5px;
    border-radius: 50%;
    margin-left: 10px;
    flex-shrink: 0;
}

.favorite-btn:hover {
    color: #FB8500;
    background: rgba(251, 133, 0, 0.1);
}

.favorite-btn.favorited {
    color: #FB8500;
}

.favorite-btn.favorited:hover {
    color: #e07600;
}

.game-description {
    color: #666;
    font-size: 0.95rem;
    line-height: 1.5;
    margin-bottom: 15px;
}

.game-meta {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.age-badge, .category-badge {
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.age-badge {
    background: linear-gradient(135deg, #8ECAE6, #219EBC);
    color: white;
}

.category-badge {
    background: linear-gradient(135deg, #FFDDD2, #FFB703);
    color: #023047;
}

/* Loading and Error States */
.loading-message, .error-message, .no-favorites {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.loading-message i, .error-message i, .no-favorites i {
    font-size: 4rem;
    color: #8ECAE6;
    margin-bottom: 20px;
    display: block;
}

.error-message i {
    color: #FB8500;
}

.no-favorites i {
    color: #FFB703;
}

.loading-message h3, .error-message h3, .no-favorites h3 {
    font-size: 1.8rem;
    color: #023047;
    margin-bottom: 15px;
    font-weight: 600;
}

.loading-message p, .error-message p, .no-favorites p {
    font-size: 1.1rem;
    margin-bottom: 25px;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-title {
        font-size: 2.2rem;
    }
    
    .page-subtitle {
        font-size: 1.1rem;
    }
    
    .filters-content {
        flex-direction: column;
        gap: 20px;
    }
    
    .filter-group {
        flex-direction: column;
        text-align: center;
        gap: 8px;
    }
    

    
    .games-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .game-card {
        max-width: 400px;
        margin: 0 auto;
    }
    
    .filters-section {
        position: static;
    }
}

@media (max-width: 480px) {
    .page-header {
        padding: 40px 0;
    }
    
    .page-title {
        font-size: 1.8rem;
    }
    
    .page-subtitle {
        font-size: 1rem;
    }
    
    .games-section {
        padding: 30px 0;
    }
    
    .game-info {
        padding: 15px;
    }
    
    .game-title {
        font-size: 1.1rem;
    }
    
    .game-meta {
        justify-content: center;
    }
}

/* Animation for game cards appearing */
.game-card {
    animation: fadeInUp 0.6s ease forwards;
    opacity: 0;
    transform: translateY(30px);
}

.game-card:nth-child(1) { animation-delay: 0.1s; }
.game-card:nth-child(2) { animation-delay: 0.2s; }
.game-card:nth-child(3) { animation-delay: 0.3s; }
.game-card:nth-child(4) { animation-delay: 0.4s; }
.game-card:nth-child(5) { animation-delay: 0.5s; }
.game-card:nth-child(6) { animation-delay: 0.6s; }

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Logo link styling */
.logo h1 a {
    color: inherit;
    text-decoration: none;
}

.logo h1 a:hover {
    color: #8ECAE6;
    transition: color 0.3s ease;
}
